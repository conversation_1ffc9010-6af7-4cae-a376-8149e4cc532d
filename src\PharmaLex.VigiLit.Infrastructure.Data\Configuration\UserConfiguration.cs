﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.UserManagement;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class UserConfiguration : EntityBaseMap<User>
{
    public override void Configure(EntityTypeBuilder<User> builder)
    {
        base.Configure(builder);

        builder.ToTable("Users", t => t.IsTemporal());

        builder.Property(e => e.Email)
            .IsRequired()
            .HasMaxLength(256);

        builder.HasIndex(x => x.Email).HasDatabaseName("UX_Users_Email").IsUnique();

        builder.Property(e => e.FamilyName).HasMaxLength(512);

        builder.Property(e => e.GivenName).HasMaxLength(512);

        builder.Property(e => e.LastLoginDate).HasColumnType("datetime2");

        builder.HasMany(u => u.ClaimsInternal)
            .WithMany(c => c.UsersInternal)
            .UsingEntity(uc => uc.ToTable("UserClaims"));

        builder.Property(e => e.QCPercentage).HasDefaultValue(10);
    }
}