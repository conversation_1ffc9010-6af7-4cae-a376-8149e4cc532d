﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>$(MSBuildProjectName)</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="PharmaLex.DataAccess" Version="8.0.0.262" />
    <PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
  </ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.Data.Repositories"></InternalsVisibleTo>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.Infrastructure.Data"></InternalsVisibleTo>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.Application"></InternalsVisibleTo>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.Application.Services"></InternalsVisibleTo>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.Application.Tests"></InternalsVisibleTo>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.Reporting.Tests"></InternalsVisibleTo>
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\PharmaLex.Core.UserManagement\PharmaLex.Core.UserManagement.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.AiAnalysis.Entities\PharmaLex.VigiLit.AiAnalysis.Entities.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.DataAccessLayer.Base\PharmaLex.VigiLit.DataAccessLayer.Base.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement\PharmaLex.VigiLit.ImportManagement.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.ReferenceManagement\PharmaLex.VigiLit.ReferenceManagement.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.Reporting.Contracts\PharmaLex.VigiLit.Reporting.Contracts.csproj" />
	</ItemGroup>

</Project>
