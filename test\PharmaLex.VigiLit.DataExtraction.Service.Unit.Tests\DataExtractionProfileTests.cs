﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Service.QualityControl;
using Xunit;

namespace PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests;
public class DataExtractionProfileTests
{
    private readonly DataExtractionProfile _dataExtractionProfile;
    private readonly Mock<JournalTitleChecker> _journalTitleChecker = new();
    private readonly Mock<IJournalRepository> _journalRepository = new();
    private readonly Mock<ILogger<JournalTitleChecker>> _mockLogger = new();
    public DataExtractionProfileTests()
    {
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?> { { "DataExtraction:FuzzySharpJournalMatchThreshold", "90" }, })
            .Build();
        _journalRepository.Setup(x => x.GetNames()).ReturnsAsync(JournalNames());
        _journalTitleChecker = new Mock<JournalTitleChecker>(_mockLogger.Object, _journalRepository.Object, configuration);
        _dataExtractionProfile = new DataExtractionProfile(_journalTitleChecker.Object);
    }

    [Fact]
    public void Given_extracted_reference_When_Copy_is_invoked_Then_correctly_populated_dto_is_returned()
    {
        // Arrange
        var extractedReference = GetExtractedReference();

        // Act
        var result = _dataExtractionProfile.Copy(extractedReference);

        // Assert
        Assert.Equal("ABSTRACT", result.Abstract);
        Assert.Equal("AFFILIATION", result.AffiliationTextFirstAuthor);
        Assert.Equal("Author A,Author B", result.Authors);
        Assert.Equal("COUNTRY", result.CountryOfOccurrence);
        Assert.Equal(new DateTime(), result.DateRevised);
        Assert.Equal("DOI", result.Doi);
        Assert.Equal("PAGES", result.FullPagination);
        Assert.Equal("ISSN", result.Issn);
        Assert.Null(result.Issue);
        Assert.Equal("JOURNAL TITLE", result.JournalTitle);
        Assert.Equal("KEY WORDS", result.Keywords);
        Assert.Null(result.Language);
        Assert.Equal("", result.MeshHeadings);
        Assert.Equal(string.Empty, result.PublicationType);
        Assert.Equal((ushort?)1982, result.PublicationYear);
        Assert.Equal(string.Empty, result.SourceId);
        Assert.Equal(4, result.SourceSystem);
        Assert.Equal("TITLE", result.Title);
        Assert.Equal("VOLUME", result.Volume);
        Assert.Equal(string.Empty, result.VolumeAbbreviation);
    }

    [Fact]
    public void Given_extracted_reference_When_CopyFailed_is_invoked_Then_correctly_populated_confidence_levels_on_dto_is_returned()
    {
        // Arrange
        var extractedReference = GetExtractedReference();

        // Act
        var result = _dataExtractionProfile.CopyFailed(extractedReference, 0.5F);

        // Assert
        Assert.Equal(20, result.AbstractConfidence);
        Assert.Equal(30, result.AffiliationTextFirstAuthorConfidence);
        Assert.Equal(40, result.AuthorsConfidence);
        Assert.Equal(60, result.CountryOfOccurrenceConfidence);
        Assert.Equal(0, result.DateRevisedConfidence);
        Assert.Equal(70, result.DoiConfidence);
        Assert.Equal(19, result.FullPaginationConfidence);
        Assert.Equal(80, result.IssnConfidence);
        Assert.Equal(90, result.IssueConfidence);
        Assert.Equal(15, result.JournalTitleConfidence);
        Assert.Equal(16, result.KeywordsConfidence);
        Assert.Equal(0, result.LanguageConfidence);
        Assert.Equal(0, result.MeshHeadingsConfidence);
        Assert.Equal(0, result.PublicationTypeConfidence);
        Assert.Equal(18, result.PublicationYearConfidence);
        Assert.Equal(0, result.SourceIdConfidence);
        Assert.Equal(10, result.TitleConfidence);
        Assert.Equal(17, result.VolumeConfidence);
        Assert.Equal(0, result.VolumeAbbreviationConfidence);
    }

    [Fact]
    public void Given_extracted_reference_When_CopyFailed_is_invoked_Then_correctly_populated_values_on_dto_is_returned()
    {
        // Arrange
        var extractedReference = GetExtractedReference();

        // Act
        var result = _dataExtractionProfile.CopyFailed(extractedReference, 0.5F);

        // Assert
        Assert.Equal("ABSTRACT", result.Abstract);
        Assert.Equal("AFFILIATION", result.AffiliationTextFirstAuthor);
        Assert.Equal("Author A,Author B", result.Authors);
        Assert.Equal("COUNTRY", result.CountryOfOccurrence);
        Assert.Equal(new DateTime(), result.DateRevised);
        Assert.Equal("DOI", result.Doi);
        Assert.Equal("PAGES", result.FullPagination);
        Assert.Equal("ISSN", result.Issn);
        Assert.Null(result.Issue);
        Assert.Equal("JOURNAL TITLE", result.JournalTitle);
        Assert.Equal("KEY WORDS", result.Keywords);
        Assert.Null(result.Language);
        Assert.Equal("", result.MeshHeadings);
        Assert.Equal(string.Empty, result.PublicationType);
        Assert.Equal((ushort?)1982, result.PublicationYear);
        Assert.Equal(string.Empty, result.SourceId);
        Assert.Equal(4, result.SourceSystem);
        Assert.Equal("TITLE", result.Title);
        Assert.Equal("VOLUME", result.Volume);
        Assert.Equal(string.Empty, result.VolumeAbbreviation);
    }

    [Fact]
    public void Given_extracted_reference_When_CopyFailed_is_invoked_Then_flags_on_dto_are_true_when_passing_minimum_confidence_is_returned()
    {
        // Arrange
        var extractedReference = GetExtractedReference();
        extractedReference.Abstract.Confidence = 0.6F;
        extractedReference.CountryOfOccurrence.Confidence = 0.6F;
        extractedReference.JournalTitle.Confidence = 0.6F;
        extractedReference.Title.Confidence = 0.6F;

        // Act
        var result = _dataExtractionProfile.CopyFailed(extractedReference, 0.5F);

        // Assert
        Assert.True(result.AbstractConfidenceCheckPassed);
        Assert.True(result.CountryOfOccurrenceConfidenceCheckPassed);
        Assert.True(result.JournalTitleConfidenceCheckPassed);
        Assert.True(result.TitleConfidenceCheckPassed);
    }

    [Fact]
    public void Given_extracted_reference_When_CopyFailed_is_invoked_Then_flags_on_dto_are_false_when_not_passing_minimum_confidence_is_returned()
    {
        // Arrange
        var extractedReference = GetExtractedReference();
        extractedReference.Abstract.Confidence = 0.1F;
        extractedReference.CountryOfOccurrence.Confidence = 0.1F;
        extractedReference.JournalTitle.Confidence = 0.1F;
        extractedReference.Title.Confidence = 0.1F;

        // Act
        var result = _dataExtractionProfile.CopyFailed(extractedReference, 0.5F);

        // Assert
        Assert.False(result.AbstractConfidenceCheckPassed);
        Assert.False(result.CountryOfOccurrenceConfidenceCheckPassed);
        Assert.False(result.JournalTitleConfidenceCheckPassed);
        Assert.False(result.TitleConfidenceCheckPassed);
    }

    private static ExtractedReference GetExtractedReference()
    {
        var extractedReference = new ExtractedReference
        {
            Title = new Title()
            {
                Value = "TITLE",
                Confidence = 0.1F,
            },
            Abstract = new Abstract()
            {
                Value = "ABSTRACT",
                Confidence = 0.2F
            },
            Affiliations = new Affiliation[]
            {
                new Affiliation()
                {
                    Value = "AFFILIATION",
                    Confidence = 0.3F,
                }
            },
            Authors = new Author[]
            {
                new Author()
                {
                    Value = "Author A",
                    Confidence = 0.4F,
                },
                new Author()
                {
                    Value = "Author B",
                    Confidence = 0.5F,
                }
            },
            CountryOfOccurrence = new CountryOfOccurrence()
            {
                Value = "COUNTRY",
                Confidence = 0.6F,
            },
            Doi = new Doi()
            {
                Value = "DOI",
                Confidence = 0.7F,
            },
            Issn = new Issn()
            {
                Value = "ISSN",
                Confidence = 0.8F
            },
            IssueNumber = new IssueNumber()
            {
                Value = "ISSUE NUMBER",
                Confidence = 0.9F
            },
            JournalTitle = new JournalTitle()
            {
                Value = "JOURNAL TITLE",
                Confidence = 0.15F,
            },
            Keywords = new Keywords()
            {
                Value = "KEY WORDS",
                Confidence = 0.16F
            },
            Volume = new Volume()
            {
                Value = "VOLUME",
                Confidence = 0.17F,
            },
            Year = new Year()
            {
                Value = "1982",
                Confidence = 0.18F,
            },
            Pages = new Pages()
            {
                Value = "PAGES",
                Confidence = 0.19F,
            }
        };
        return extractedReference;
    }

    private static List<string> JournalNames()
    {
        var journalNames = new List<string>
        {
            "Mabl Journal Edited",
            "Acta Obstétrica e Ginecológica Portuguesa",
            "Acta Portuguesa de Nutrição",
            "Acta Radiológica Portuguesa",
            "Acta Urológica Portuguesa",
            "AIMGF Magazine",
            "Alertas de Segurança (INFARMED)",
            "Angiologia e Cirurgia Vascular",
            "Biomedical and Biopharmaceutical Research",
            "Boletim de Farmacovigilância (INFARMED)",
            "Congresso de Medicina Interna",
            "Medicina Interna"
        };
        return journalNames;
    }
}
