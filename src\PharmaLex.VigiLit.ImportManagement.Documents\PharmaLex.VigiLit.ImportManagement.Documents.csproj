﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Library</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="PharmaLex.BlobStorage" Version="8.0.0.123" />
		<PackageReference Include="System.Text.Json" Version="9.0.5" />
	</ItemGroup>
	<ItemGroup>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.ImportManagement.Document.Unit.Tests"></InternalsVisibleTo>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.ImportManagement.Ui"></InternalsVisibleTo>
		<InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
	</ItemGroup>
</Project>
