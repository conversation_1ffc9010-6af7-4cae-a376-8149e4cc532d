﻿using Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using PharmaLex.VigiLit.ContractManagement.Ui.Journals;
using PharmaLex.VigiLit.ContractManagement.Ui.Services;

namespace PharmaLex.VigiLit.ContractManagement.Ui;
public static class ConfigureServices
{
    public static void RegisterContractManagementUi(this IServiceCollection services, IConfiguration configuration)
    {


        services.AddScoped<IJournalRepository, JournalRepository>();
        services.AddScoped<IJournalService, JournalService>();
        services.AddScoped<ICriteriaValidator, CriteriaValidator>();



        var assembly = typeof(ConfigureServices).Assembly;

        services.AddAutoMapper(assembly);

        services.AddControllersWithViews()
            .AddRazorRuntimeCompilation()
            .AddApplicationPart(assembly);

        services.Configure<MvcRazorRuntimeCompilationOptions>(options =>
            { options.FileProviders.Add(new EmbeddedFileProvider(assembly)); });
    }
}
