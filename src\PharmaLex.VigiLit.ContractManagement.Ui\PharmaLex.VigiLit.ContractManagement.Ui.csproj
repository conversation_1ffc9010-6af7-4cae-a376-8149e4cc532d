﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Views\Contracts\ContractHistoryVersions.cshtml" />
    <None Remove="Views\Contracts\Index.cshtml" />
    <None Remove="Views\Contracts\PrintPreview.cshtml" />
    <None Remove="Views\Shared\Components\AddEditJournalModal.cshtml" />
    <None Remove="Views\Shared\Components\JournalsTable.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Views\Contracts\ContractHistoryVersions.cshtml" />
    <EmbeddedResource Include="Views\Contracts\Index.cshtml" />
    <EmbeddedResource Include="Views\Contracts\PrintPreview.cshtml" />
    <EmbeddedResource Include="Views\Shared\Components\AddEditJournalModal.cshtml" />
    <EmbeddedResource Include="Views\Shared\Components\JournalsTable.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.10" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.Core.UserSessionManagement\PharmaLex.Core.UserSessionManagement.csproj" />
    <ProjectReference Include="..\PharmaLex.Core.Web\PharmaLex.Core.Web.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ContractManagement\PharmaLex.VigiLit.ContractManagement.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Domain.Interfaces\PharmaLex.VigiLit.Domain.Interfaces.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Logging\PharmaLex.VigiLit.Logging.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Ui.ViewModels\PharmaLex.VigiLit.Ui.ViewModels.csproj" />
  </ItemGroup>
	<ItemGroup>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.ContractManagement.Ui.Unit.Tests"></InternalsVisibleTo>
		<InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="Journal\" />
	</ItemGroup>

</Project>
