﻿using PharmaLex.Core.UserManagement.Claims;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.Application.UserManagement.Users;

public interface IUserService
{
    Task<UserModel> Create(string givenName, string familyName, string email, List<int> claimIds);
    Task Update(ClaimsPrincipal claimsPrincipal, int id, string givenName, string familyName, string email, List<int> claimIds);
    Task Update(UserModel userModel);
    Task RecordLogin(string email);
    Task<List<UserFullModel>> GetAllAsync(ClaimsPrincipal claimsPrincipal);
    Task<UserModel> GetUser(string email);

    /// <summary>
    /// Retrieves a collection of claims filtered by the claims principal.
    /// </summary>
    /// <remarks>
    /// Where the given claims principal is a "super admin" all the non-company claims are returned.
    /// For other principals non-company claims that the claims principal holds or are not admin claims are returned.
    /// </remarks>
    /// <param name="claimsPrincipal">The claims principal.</param>
    /// <returns>A collection of claims</returns>
    Task<List<ClaimModel>> GetClaimsAsync(ClaimsPrincipal claimsPrincipal);

    Task<List<AssessorModel>> GetAssessorsAsync();
    Task<AssessorModel> GetAssessorAsync(int id);
    Task UpdateAssessor(int id, IEnumerable<int> selectedSubstanceIds, int qualityCheckPercentage);
    Task<IEnumerable<UserLocksModel>> GetUserLocks();
    Task<UserModel> GetCompanyUser(string email, int companyId);
    Task<UserModel> GetInactiveCompanyUser(string email);
    Task<ClaimModel> GetClaimByNameAsync(string name);

}