﻿@using PharmaLex.VigiLit.Domain.Enums
<script type="text/x-template" id="reference-info-template">
    <div class="reference-info-container py-0">
        <div>
            <h3 class="mt-2 mb-1">{{referenceClassification.substance.name}}</h3>
            <div class="flex flex-gap pb-2">
                <div class="reference-group">
                    <label>PLX ID</label>
                    <span><a :href="`./References/Classifications/${referenceClassification.id}`" target="_blank">{{referenceClassification.id}}</a></span>
                </div>
                <div class="reference-group">
                    <label>Source Id</label>
                    <span v-if="pubmedSource"><a :href="`https://pubmed.ncbi.nlm.nih.gov/${referenceClassification.reference.sourceId}`" target="_blank" rel="noopener">{{referenceClassification.reference.sourceId}}</a></span>
                    <span v-else><a :href="`./References/Classifications/${referenceClassification.id}`" target="_blank">{{referenceClassification.reference.sourceId}}</a></span>
                </div>

                <div class="reference-group" v-if="referenceUpdate">
                    <label>DOI</label>
                    <span v-if="referenceUpdate.doi">{{referenceUpdate.doi}}</span>
                    <span v-else>N/A</span>
                </div>
                <div class="reference-group" v-if="!referenceUpdate">
                    <label>DOI</label>
                    <span v-if="referenceClassification.reference.doi">{{referenceClassification.reference.doi}}</span>
                    <span v-else>N/A</span>
                </div>

                <div class="flex flex-gap flex-align-center">
                    <div v-if="referenceUpdate">
                        <label>Modified</label>
                        <span v-if="pubmedSource">{{getDateFormat(referenceUpdate.dateRevised)}} <abbr title="Eastern Time">ET</abbr></span>
                        <span v-else>N/A</span>
                    </div>
                    <div v-if="!referenceUpdate">
                        <label>Modified</label>
                        <span v-if="pubmedSource">{{getDateFormat(referenceClassification.reference.dateRevised)}} <abbr title="Eastern Time">ET</abbr></span>
                        <span v-else>N/A</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex flex-gap flex-grow-1 justify-end flex-align-center">
            
            <audit-trail-selection v-if="fileSource"
                v-on:translationSelected="downloadTranslation">
            </audit-trail-selection>

            <span v-if="referenceUpdate" class="state-indicator-updated">Updated</span>
            <span :class="[`state-indicator-${referenceClassification.referenceStateText.toLowerCase()}`]">{{referenceClassification.referenceStateText}}</span>
        </div>
    </div>
</script>
<script type="text/javascript">
    vueApp.component('reference-info', {
        template: '#reference-info-template',
        props: {
            referenceClassification: {
                type: Object
            },
            referenceUpdate: {
                type: Object
            }
        },
        emits: ['translation'],
        data() {
            return {
                documentLocation: {}
            }
        },
        methods: {
            getDateFormat: function (date) {
                return moment.utc(date).format('DD MMM YYYY');
            },
            downloadTranslation: function () {

                this.$emit('translation', this.documentLocation);
            }
        },
        computed: {
            pubmedSource() {
                return (this.referenceClassification && this.referenceClassification.reference.sourceSystem == '@((Int16)SourceSystem.PubMed)') || (this.referenceUpdate && this.referenceUpdate.sourceSystem == '@((Int16)SourceSystem.PubMed)');
            },
            fileSource() {
                return (this.referenceClassification && this.referenceClassification.reference.sourceSystem == '@((Int16)SourceSystem.File)') || (this.referenceUpdate && this.referenceUpdate.sourceSystem == '@((Int16)SourceSystem.File)');
            }
        },
        created() {
            //console.log(this.referenceClassification);

            this.documentLocation = this.referenceClassification.reference.documentLocation;
        }
    });
</script>
