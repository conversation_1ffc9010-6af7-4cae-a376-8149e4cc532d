﻿using AutoMapper;
using PharmaLex.VigiLit.ImportManagement.Documents;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Ui.Generics;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

namespace PharmaLex.VigiLit.ImportManagement.Ui.FailedImportFiles;

internal class FailedImportFileService : GenericCardService<IGenericCardRepository<FailedImportFile>, FailedImportFile, FailedImportFileModel>, IFailedImportFileService
{
    private readonly IImportFileDocumentService _importFileDocumentUploadService;
    public FailedImportFileService(
        IGenericCardRepository<FailedImportFile> failedImportFileRepository,
        IImportReferenceRepository importReferenceRepository,
        IMapper mapper,
        IImportFileDocumentService importFileDocumentUploadService) : base(failedImportFileRepository, importReferenceRepository, mapper)
    {
        _importFileDocumentUploadService = importFileDocumentUploadService;
    }

    public override async Task<List<ImportDisplayCard>> GetCards()
    {
        var displayCards = new List<ImportDisplayCard>();
        var failedImportFiles = await GetAll();
        foreach (var failedImportFile in failedImportFiles)
        {
            displayCards.Add(new ImportDisplayCard()
            {
                Id = failedImportFile.Id.ToString(),
                Filename = failedImportFile.Filename!,
                ImportType = "Failed",
                Title = failedImportFile.JournalTitle ?? string.Empty,
                DateFrom = "",
                DateTo = "",
                FileCount = 0,
                FilesPlus = 0,
                CreatedBy = failedImportFile.CreatedBy,
                LastUpdatedBy = failedImportFile.LastUpdatedBy,
                FailedImportStatus = (int)failedImportFile.Status
            });
        }

        return displayCards;
    }

    public async Task<FailedImportFile?> GetByDoi(string doi)
    {
        var failedImportFiles = await _repository.GetAll();
        return failedImportFiles.FirstOrDefault(x => x.Doi == doi);
    }


    public async Task<DownloadFile> Download(Guid batchId, string fileName)
    {
        var pdfBytes = await RetrieveDocument(batchId, fileName);
        return new DownloadFile
        {
            FileName = fileName,
            ContentType = "application/pdf",
            Bytes = pdfBytes
        };
    }

    private async Task<byte[]> RetrieveDocument(Guid batchId, string fileName)
    {
        var importFileDocumentUploadDescriptor = new ImportFileDescriptor(batchId, fileName);
        using var stream = await _importFileDocumentUploadService.OpenRead(importFileDocumentUploadDescriptor);

        using var memoryStream = new MemoryStream();
        await stream.CopyToAsync(memoryStream);

        return memoryStream.ToArray();
    }
}
