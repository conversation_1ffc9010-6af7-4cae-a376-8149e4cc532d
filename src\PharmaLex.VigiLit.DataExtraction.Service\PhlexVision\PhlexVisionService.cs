﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using PharmaLex.VigiLit.Logging;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Mime;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using static PharmaLex.VigiLit.DataExtraction.Service.PhlexVision.PhlexVisionConstants;

namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;
internal class PhlexVisionService : IPhlexVisionService
{
    private readonly ILogger<PhlexVisionService> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IConfiguration _configuration;

    public PhlexVisionService(ILogger<PhlexVisionService> logger, IHttpClientFactory httpClientFactory, IConfiguration configuration)
    {
        _logger = logger;
        _httpClientFactory = httpClientFactory;
        _configuration = configuration;
    }

    public async Task RequestDataExtraction(ExtractRequest extractRequest)
    {
        var jwtToken = GetJwtToken();

        _logger.LogInformation("Retrieved JWT token");

        using HttpRequestMessage request = new(HttpMethod.Post, _configuration[PhlexVisionUrl]);
        using HttpClient client = _httpClientFactory.CreateClient();

        var json = GetDocumentProcessModel(jwtToken, extractRequest);
        HttpContent content = new StringContent(
            json,
            Encoding.UTF8,
            MediaTypeNames.Application.Json);

        request.Content = content;
        var consumerKey = _configuration[ConsumerKeyHeaderValue];
        request.Headers.Add(ConsumerKeyHeader, consumerKey);
        request.Headers.Add(AuthorizationHeader, $"Bearer {jwtToken}");
        request.Headers.Add(CorrelationIdHeader, extractRequest.CorrelationId.ToString());

        var response = await client.SendAsync(request);

        HandleDocumentProcessRequestResponse(response);

        _logger.LogInformation("PhlexVisionService: API Response: {StatusCode} - {Response}", LogSanitizer.Sanitize(response.StatusCode.ToString()), LogSanitizer.Sanitize(response.ToString()));
    }

#pragma warning disable S6781 // key is coming from Secrets or KV
    private string GetJwtToken()
    {
        var keySecretValue = _configuration[SecretValue] ?? throw new ArgumentException("keySecretValue not set");

        SymmetricSecurityKey key = new(Encoding.UTF8.GetBytes(keySecretValue));
        SigningCredentials signingCredentials = new(key, SecurityAlgorithms.HmacSha256, SecurityAlgorithms.Sha256Digest);

        JwtSecurityToken token = new(
            claims: new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, "OCR"),
            },
            expires: DateTime.UtcNow.AddHours(24),
            notBefore: DateTime.UtcNow,
            signingCredentials: signingCredentials);

        var tokenVal = new JwtSecurityTokenHandler().WriteToken(token);
        return tokenVal;
    }
#pragma warning restore S6781

    private void HandleDocumentProcessRequestResponse(HttpResponseMessage response)
    {
        if (!response.IsSuccessStatusCode)
        {
            _logger.LogInformation("PhlexVisionService: Failed");
        }
        else
        {
            var correlationId = new Guid(response.Headers.NonValidated[CorrelationIdHeader].First());

            _logger.LogInformation("PhlexVisionService: CorrelationId : {CorrelationId}", correlationId);
            _logger.LogInformation("PhlexVisionService: Successfully submitted");
        }
    }

    private string GetDocumentProcessModel(string token, ExtractRequest extractRequest)
    {
        var baseUrl = _configuration[PhlexVisionCallbackBaseUrl];

        var documentLocationUrl = $"{baseUrl}DownloadDocument/{extractRequest.BatchId}/{extractRequest.FileName}";

        var result = JsonSerializer.Serialize(
            new
            {
                documentId = extractRequest.BatchId,
                DocumentDownloadUrl = documentLocationUrl,
                successCallbackUrl = $"{baseUrl}success/{extractRequest.BatchId}",
                errorCallbackUrl = $"{baseUrl}error/{extractRequest.BatchId}",
                BearerToken = token,
                ConfigId = _configuration[OpenAiConfigId],
                Stages = (string[])["Translate", "MetadataExtractor"],
                Priority = "Urgent",
                FileExtension = "pdf"
            });

        return result;
    }
}
