﻿using AutoMapper;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.ReferenceManagement;

namespace PharmaLex.VigiLit.ImportManagement.Service.Services;

#pragma warning disable S107

internal class UnIndexedReferenceImporter(
                IImportingImportRepository importRepository,
                IImportingImportContractRepository importContractRepository,
                IImportReferenceRepository importReferenceRepository,
                IReferenceRepository referenceRepository,
                IReferenceClassificationRepository referenceClassificationRepository,
                IReferenceHistoryActionRepository referenceHistoryActionRepository,
                IImportingImportContractReferenceClassificationRepository importContractReferenceClassificationRepository,
                IContractMatcherService contractMatcherService,
                IMapper mapper,
                TimeProvider timeProvider)
    : IUnIndexedReferenceImporter
{
    public async Task ProcessEnqueuedImports()
    {
        var importReference = await importReferenceRepository.GetNextQueued();

        if (importReference != null)
        {

            var matchingContracts = await contractMatcherService.FindMatchingContractVersions(importReference);

            if (matchingContracts.Count > 0)
            {
                var reference = mapper.Map<Reference>(importReference);
                await SaveImportAndReferenceClassification(reference, matchingContracts);
            }

            importReference.StatusType = ImportReferenceStatusType.Completed;

            await importReferenceRepository.SaveChangesAsync();
        }
    }

    private async Task SaveImportAndReferenceClassification(Reference reference, List<ContractVersion> contractVersions)
    {
        var import = await SaveImport((SourceSystem)reference.SourceSystem);

        var importContracts = await SaveImportContracts(import, contractVersions);

        await SaveReference(reference);

        var referenceClassifications = await SaveReferenceClassifications(reference, contractVersions);

        await SaveReferenceHistoryActions(referenceClassifications);

        await SaveImportContractReferenceClassifications(referenceClassifications, importContracts, contractVersions, import);

        await SendToAi(reference, importContracts, contractVersions);
    }

    private async Task SaveReferenceHistoryActions(IList<ReferenceClassification> referenceClassifications)
    {
        foreach (var referenceClassification in referenceClassifications)
        {
            referenceHistoryActionRepository.Add(new ReferenceHistoryAction
            {
                ReferenceClassification = referenceClassification,
                ReferenceHistoryActionType = ReferenceHistoryActionType.New
            });
        }

        await referenceHistoryActionRepository.SaveChangesAsync();
    }

    private async Task SaveImportContractReferenceClassifications(IList<ReferenceClassification> referenceClassifications, 
                                                                    IEnumerable<ImportContract> importContracts, List<ContractVersion> contractVersions, Import import)
    {
        foreach (var importContract in importContracts)
        {
            var substanceIds =
                contractVersions.Where(x => x.ContractId == importContract.ContractId)
                    .Select(x => x.Contract.SubstanceId);

            foreach (var substanceId in substanceIds)
            {
                importContractReferenceClassificationRepository.Add(new ImportContractReferenceClassification
                {
                     ImportId = import.Id,
                     ReferenceClassificationId = referenceClassifications.Single(x => x.SubstanceId == substanceId).Id,
                     ICRCType = ICRCType.New,
                     ImportContractId = importContract.Id
                });
            }

            // Note: should be outside loop for efficiency. Unfortunately have to save one at a time due to trigger on ImportContractReferenceClassifications causing EF failure
            await importContractReferenceClassificationRepository.SaveChangesAsync();
        }
    }

    private async Task<IList<ReferenceClassification>> SaveReferenceClassifications(Reference newReference,
        List<ContractVersion> contractVersions)
    {
        var uniqueSubstances = contractVersions.GroupBy(x => x.Contract.SubstanceId).Select(x => x.Key).ToList();

        var referenceClassifications = uniqueSubstances.Select(x => new ReferenceClassification(newReference.Id, x)).ToArray();

        foreach (var referenceClassification in referenceClassifications)
        {
            referenceClassificationRepository.Add(referenceClassification);
        }

        await referenceClassificationRepository.SaveChangesAsync();

        return referenceClassifications;
    }

    private async Task<IList<ImportContract>> SaveImportContracts(Import import, List<ContractVersion> contractVersions)
    {
        var importContracts = contractVersions
            .Select(cv => ImportContract.CreateCompletedImportContract(import, cv, timeProvider)).ToArray();

        foreach (var importContract in importContracts)
        {
            importContractRepository.Add(importContract);
        }

        await importContractRepository.SaveChangesAsync();

        return importContracts;
    }

    private async Task SaveReference(Reference reference)
    {
        reference.SourceId = Guid.NewGuid().ToString();

        referenceRepository.Add(reference);
        await referenceRepository.SaveChangesAsync();
    }

    private async Task<Import> SaveImport(SourceSystem sourceSystem)
    {
        var importType = sourceSystem switch
        {
            SourceSystem.Web => ImportType.Web,
            SourceSystem.File => ImportType.File,
            _ => ImportType.Manual
        };

        var import = Import.CreateCompletedImport(timeProvider, importType);

        importRepository.Add(import);
        await importRepository.SaveChangesAsync();

        return import;
    }

    private async Task SendToAi(Reference reference, IList<ImportContract> importContracts, List<ContractVersion> contractVersions)
    {
        foreach (var importContract in importContracts)
        {
            importContract.Contract = contractVersions.Single(x => x.ContractId == importContract.ContractId).Contract;
        }

        await contractMatcherService.SendToAi(reference, importContracts);
    }
}

#pragma warning restore S107