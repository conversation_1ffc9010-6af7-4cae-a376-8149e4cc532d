﻿using AutoMapper;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
using PharmaLex.VigiLit.ReferenceManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.Services;

public class ClassificationService : IClassificationService
{
    private readonly IMapper _mapper;
    private readonly IVigiLitUserContext _userContext;

    private readonly IImportContractReferenceClassificationRepository _importContractReferenceClassificationRepository;
    private readonly IReferenceClassificationRepository _referenceClassificationRepository;
    private readonly IReferenceRepository _referenceRepository;
    private readonly ISubstanceRepository _substanceRepository;
    private readonly IImportRepository _importRepository;
    private readonly IReferenceHistoryActionRepository _referenceHistoryActionRepository;
    private readonly ICompanyInterestRepository _companyInterestRepository;
    private readonly IPotentialCaseAdditionalFieldService _potentialCaseAdditionalFieldService;

    public ClassificationService(
        IMapper mapper,
        IVigiLitUserContext userContext,

        IImportContractReferenceClassificationRepository importContractReferenceClassificationRepository,
        IReferenceClassificationRepository referenceClassificationRepository,
        IReferenceRepository referenceRepository,
        ISubstanceRepository substanceRepository,
        IImportRepository importRepository,
        IReferenceHistoryActionRepository referenceHistoryActionRepository,
        ICompanyInterestRepository companyInterestRepository,
        IPotentialCaseAdditionalFieldService potentialCaseAdditionalFieldService
        )
    {
        _mapper = mapper;
        _userContext = userContext;

        _importContractReferenceClassificationRepository = importContractReferenceClassificationRepository;
        _referenceClassificationRepository = referenceClassificationRepository;
        _referenceRepository = referenceRepository;
        _substanceRepository = substanceRepository;
        _referenceHistoryActionRepository = referenceHistoryActionRepository;
        _importRepository = importRepository;
        _companyInterestRepository = companyInterestRepository;
        _potentialCaseAdditionalFieldService = potentialCaseAdditionalFieldService;
    }

    public async Task<ReferenceClassification> GetByIdAsync(int referenceClassificationId)
    {
        return await _referenceClassificationRepository.GetByIdAsync(referenceClassificationId);
    }

    public async Task Classify(ReferenceClassificationModel updatedClassification)
    {
        var classification = await _referenceClassificationRepository.GetByIdAsync(updatedClassification.Id);

        if (HasClassificationCategoryChanged(updatedClassification, classification))
        {
            // Add Reference History Action - Master accessor edits classification
            AddReferenceHistoryAction(classification.Id, ReferenceHistoryActionType.Corrected);
        }

        classification.Classify(
            updatedClassification.ClassificationCategoryId,
            updatedClassification.PotentialCaseAdditionalInformation,
            updatedClassification.DosageForm,
            updatedClassification.CountryOfOccurrence,
            _userContext.UserId,
            updatedClassification.MinimalCriteria);

        // Add Reference History Action - Approved
        AddReferenceHistoryAction(classification.Id, ReferenceHistoryActionType.Approved);

        await _referenceClassificationRepository.SaveChangesAsync();
        await _referenceHistoryActionRepository.SaveChangesAsync();
    }

    public async Task<IEnumerable<ReferenceClassificationWithReferenceModel>> GetReferencesToClassify(int importId)
    {
        // All of type potential case
        var referencesToClassify = await _referenceClassificationRepository.GetPreclassifiedPotentialCasesForApproval(importId);

        // Calculate quality check sample sizes for all users who have pre-classified something
        var assessorQualityCheckStatusReports = await _referenceClassificationRepository.GetAssessorQualityCheckStatusReports(importId);

        foreach (var item in assessorQualityCheckStatusReports)
        {
            // Quality check sample per pre-assessor
            var sample = await _referenceClassificationRepository.GetPreclassifiedQualityCheckSample(item.AssessorId, item.ItemsToTake, importId);
            referencesToClassify = referencesToClassify.Concat(sample);
        }

        return _mapper.Map<IEnumerable<ReferenceClassificationWithReferenceModel>>(referencesToClassify);
    }

    public async Task Sign(int importId)
    {
        int batchSize = 250;

        // Sign off icrc & classifications in batches as there may be tens of thousands total.
        var icrcs_batch = await _importContractReferenceClassificationRepository.GetForSigning(importId, batchSize);

        // loop until there are no more records to sign.
        while (icrcs_batch.Any())
        {
            // edit the records in this batch
            foreach (var icrc in icrcs_batch)
            {
                // sign
                icrc.ReferenceClassification.Sign();

                // log reference history action
                AddReferenceHistoryAction(icrc.ReferenceClassification.Id, ReferenceHistoryActionType.Signed);

                // log email relevant event
                await AddEmailRelevantEvent(icrc.ReferenceClassification.Id, EmailRelevantEventActionType.Signed, icrc.ReferenceClassification.ClassificationCategoryId);
            }

            // save changes and clear change trackers
            await _importContractReferenceClassificationRepository.SaveChangesAsync();
            await _referenceHistoryActionRepository.SaveChangesAsync();
            await _companyInterestRepository.SaveChangesAsync();

            _importContractReferenceClassificationRepository.ClearChangeTracker();
            _referenceHistoryActionRepository.ClearChangeTracker();
            _companyInterestRepository.ClearChangeTracker();

            // load next batch
            icrcs_batch = await _importContractReferenceClassificationRepository.GetForSigning(importId, batchSize);
        }

        // Signing off the import makes the dashboard card's archive button appear and the sign button disappear.
        var import = await _importRepository.GetById(importId);
        import.ImportDashboardStatusType = ImportDashboardStatusType.Signed;
        await _importRepository.SaveChangesAsync();
    }

    public async Task ReClassify(ReferenceClassificationModel updatedClassification)
    {
        var classification = await _referenceClassificationRepository.GetByIdAsync(updatedClassification.Id);

        if (classification == null)
        {
            throw new ArgumentException($"Classification with id {updatedClassification.Id} not found");
        }

        if (HasClassificationCategoryChanged(updatedClassification, classification))
        {
            // Add Reference History Action - Master accessor edits classification
            AddReferenceHistoryAction(classification.Id, ReferenceHistoryActionType.Corrected);
        }

        classification.ReClassify(
            updatedClassification.ClassificationCategoryId,
            updatedClassification.PotentialCaseAdditionalInformation,
            updatedClassification.DosageForm,
            updatedClassification.CountryOfOccurrence,
            _userContext.UserId,
            updatedClassification.MinimalCriteria,
            updatedClassification.ReasonForChange);


        // Add Reference History Action - Approved
        AddReferenceHistoryAction(classification.Id, ReferenceHistoryActionType.Approved);

        await _referenceClassificationRepository.SaveChangesAsync();
        await _referenceHistoryActionRepository.SaveChangesAsync();
    }

    public async Task EditFromReferenceHistory(ReferenceClassificationModel updatedClassification)
    {
        var classification = await _referenceClassificationRepository.GetByIdAsync(updatedClassification.Id);

        if (classification == null)
        {
            throw new ArgumentException($"Classification with id {updatedClassification.Id} not found");
        }

        classification.ReferenceState = ReferenceState.Reclassified;

        classification.Edit(
            updatedClassification.ClassificationCategoryId,
            updatedClassification.DosageForm,
            updatedClassification.CountryOfOccurrence,
            updatedClassification.PSURRelevanceAbstract,
            updatedClassification.PvSafetyDatabaseId,
            _userContext.UserId,
            updatedClassification.MinimalCriteria,
            updatedClassification.ReasonForChange);

        // log reference history action
        AddReferenceHistoryAction(classification.Id, ReferenceHistoryActionType.ReClassified);

        // log email relevant event
        await AddEmailRelevantEvent(classification.Id, EmailRelevantEventActionType.Edited, updatedClassification.ClassificationCategoryId);

        await _referenceClassificationRepository.SaveChangesAsync();
        await _referenceHistoryActionRepository.SaveChangesAsync();
        await _companyInterestRepository.SaveChangesAsync();
    }

    public async Task<int> GetClassifiedCount(int importId)
    {
        return await _referenceClassificationRepository.GetClassifiedCount(importId);
    }

    public async Task<int> GetToDoCount(int importId)
    {
        return await _referenceClassificationRepository.GetTodoCount(importId);
    }

    public async Task<int> GetPreclassifiedCount(int importId)
    {
        return await _referenceClassificationRepository.GetPreclassifiedCount(_userContext.UserId, importId);
    }

    public async Task<IEnumerable<ReferenceClassificationWithReferenceModel>> GetAllClassified(int importId)
    {
        return await _referenceClassificationRepository.GetAllClassified(importId);
    }

    private async Task<IEnumerable<ReferenceHistoryActionModel>> GetReferenceHistoryActions(int referenceClassificationId, User user)
    {
        var actions = await _referenceHistoryActionRepository.GetReferenceActions(referenceClassificationId);

        // Hide user names from users with Client Researcher role.
        if (user.IsCompanyUser())
        {
            foreach (var action in actions)
            {
                // Don't override System
                if (action.UserId > 0)
                {
                    action.UserName = string.Format("User {0}", action.UserId);
                }
            }
        }

        return actions;
    }

    public async Task<ReferenceHistoryDetailsPageModel> GetReferenceHistoryDetailsInitialPageLoad(int referenceClassificationId, User user)
    {
        var referenceHistoryActions = await GetReferenceHistoryActions(referenceClassificationId, user);
        var latestAction = referenceHistoryActions.First();
        var previousActionId = 0;

        if (referenceHistoryActions.Count() > 1)
        {
            var previousAction = referenceHistoryActions.Skip(1).First();
            previousActionId = previousAction.Id;
        }

        var model = await GetReferenceHistoryDetailsByAction(referenceClassificationId, latestAction.Id, previousActionId, user);
        model.HistoryActions = referenceHistoryActions;
        model.ReferenceClassification.Substance = _mapper.Map<SubstanceSimpleModel>(await _substanceRepository.GetByIdAsync(model.ReferenceClassification.SubstanceId));
        model.PotentialCaseAdditionalFields = await _potentialCaseAdditionalFieldService.GetAllAsync();

        return model;
    }

    public async Task<ReferenceHistoryDetailsPageModel> GetReferenceHistoryDetailsByAction(int referenceClassificationId, int actionId, int previousActionId, User user)
    {
        var historyAction = await _referenceHistoryActionRepository.GetByIdAsync(actionId);

        if (historyAction == null)
        {
            throw new ArgumentOutOfRangeException($"History action with Id {actionId} does not exist.");
        }

        var model = new ReferenceHistoryDetailsPageModel();

        model.ReferenceClassification = await _referenceClassificationRepository.GetTemporalAsOfAsync(historyAction.TimeStamp, referenceClassificationId);
        model.ReferenceDetails = await _referenceRepository.GetReferenceSnapshot(model.ReferenceClassification.ReferenceId, historyAction.TimeStamp);
        model.IsEditable = await CanEditReferenceClassification(referenceClassificationId);

        if (previousActionId != 0)
        {
            var previousHistoryAction = await _referenceHistoryActionRepository.GetByIdAsync(previousActionId);

            if (previousHistoryAction != null)
            {
                model.PreviousReferenceDetails = await _referenceRepository.GetReferenceSnapshot(model.ReferenceClassification.ReferenceId, previousHistoryAction.TimeStamp);
            }
        }

        // Hide user names from users with Client Researcher role.
        if (user.IsCompanyUser())
        {
            model.ReferenceClassification.PreclassifierName = string.Empty;
        }

        return model;
    }

    public async Task<bool> CanEditReferenceClassification(int id)
    {
        return await _referenceClassificationRepository.GetReferenceClassificationForEdit(id) != null;
    }

    public void AddReferenceHistoryAction(int classificationId, ReferenceHistoryActionType historyActionType)
    {
        var historyAction = new ReferenceHistoryAction(classificationId, historyActionType, _userContext.UserId);
        _referenceHistoryActionRepository.Add(historyAction);
    }

    public async Task SaveReferenceHistoryChangesAsync()
    {
        await _referenceHistoryActionRepository.SaveChangesAsync();
    }

    private async Task AddEmailRelevantEvent(int classificationId, EmailRelevantEventActionType actionType, int? classificationCategoryId)
    {
        var companyInterests = await _companyInterestRepository.GetForLoggingEmailRelevantEvent(classificationId);

        foreach (var companyInterest in companyInterests)
        {
            companyInterest.AddEmailRelevantEvent(actionType, classificationCategoryId);
        }
    }

    private static bool HasClassificationCategoryChanged(ReferenceClassificationModel a, ReferenceClassification b)
    {
        return a.ClassificationCategoryId != b.ClassificationCategoryId;
    }
}
