﻿using AutoMapper;
using PharmaLex.Authentication.B2C;
using PharmaLex.Core.UserManagement.Claims;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.VigiLit.Application.Extensions;
using PharmaLex.VigiLit.Domain.Exceptions;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Claim = PharmaLex.VigiLit.Domain.UserManagement.Claim;
using ClaimTypes = PharmaLex.VigiLit.Domain.UserManagement.ClaimTypes;

namespace PharmaLex.VigiLit.Application.UserManagement.Users;

public class UserService : IUserService
{
    private readonly IUserRepository _userRepository;
    private readonly IClaimRepository _claimsRepository;
    private readonly IUserSessionService _userSessionService;
    private readonly IMapper _mapper;

    public UserService(IUserRepository userRepository,
        IClaimRepository claimsRepository,
        IUserSessionService userSessionService,        
        IMapper mapper)
    {
        _userRepository = userRepository;
        _claimsRepository = claimsRepository;
        _userSessionService = userSessionService;
        _mapper = mapper;
    }

    public async Task<UserModel> Create(string givenName, string familyName, string email, List<int> claimIds)
    {
        var user = new User(givenName, familyName, email);

        if (claimIds != null && claimIds.Any())
        {
            var claims = await _claimsRepository.GetAllAsync();

            var selectedClaims = claims.Where(c => claimIds.Contains(c.Id));

            if (claimIds.Count != selectedClaims.Count())
            {
                throw new ArgumentException("Invalid claimId provided");
            }

            user.AddClaims(selectedClaims);
        }

        _userRepository.Add(user);

        await _userRepository.SaveChangesAsync();

        return _mapper.Map<UserModel>(user);
    }

    public async Task Update(ClaimsPrincipal claimsPrincipal, int id, string givenName, string familyName, string email, List<int> claimIds)
    {
        var user = await _userRepository.GetById(id);

        if (user == null)
        {
            throw new ArgumentException($"User with id '{id}' not found");
        }

        if (id == claimsPrincipal.GetClaimValue<int>("plx:userid"))
        {
            throw new ArgumentException($"Self editing not allowed");
        }

        user.UpdateUserData(givenName, familyName, email);

        if (UserClaimsHaveChanged(claimIds, user))
        {
            await _userSessionService.DeleteUserSessionAsync(user.Id);
        }


        await UpdateClaimsAsync(claimsPrincipal, user, claimIds);

        await _userRepository.SaveChangesAsync();
    }

    private static bool UserClaimsHaveChanged(List<int> claimIds, User user)
    {
        var existingClaims = user.ClaimsInternal.Select(x => x.Id).ToList();

        return !existingClaims.OrderedContentsEqual(claimIds.OrderBy(x => x).ToList());
    }

    public async Task Update(UserModel userModel)
    {
        var user = await _userRepository.GetById(userModel.Id);

        if (user == null)
        {
            throw new ArgumentException($"User with id '{userModel.Id}' not found");
        }
        
        _mapper.Map(userModel, user);

        await _userRepository.SaveChangesAsync();
    }

    private async Task UpdateClaimsAsync(ClaimsPrincipal claimsPrincipal, User user, List<int> claimIds)
    {
        var availableClaims = await GetClaimEntitiesAsync(claimsPrincipal);
        var selectedClaims = availableClaims.Where(c => claimIds.Contains(c.Id));

        if (claimIds.Count != selectedClaims.Count())
        {
            throw new ArgumentException("Invalid claim provided");
        }

        var addedClaims = selectedClaims.Where(c => !user.GetClaims().Any(x => x.Id == c.Id)).ToList();
        var removedClaims = user.GetClaims().Where(c => availableClaims.Any(x => x.Id == c.Id) && !claimIds.Contains(c.Id));

        if (!claimsPrincipal.IsSuperAdmin())
        {
            foreach (var c in addedClaims.Union(removedClaims))
            {
                if (c.ClaimType == ClaimTypes.Admin && !claimsPrincipal.HasClaim(x => x.Type == c.Key))
                {
                    throw new ArgumentException("Invalid claim provided");
                }
            }
        }

        user.AddClaims(addedClaims);

        foreach (var claim in removedClaims)
        {
            user.RemoveClaim(claim.Id);
        }
    }

    public async Task<List<UserFullModel>> GetAllAsync(ClaimsPrincipal claimsPrincipal)
    {
        var users = (await _userRepository.GetAllAsync()).ToList();
        var claims = await GetClaimsAsync(claimsPrincipal);

        foreach (var user in users)
        {
            user.Claims = user.Claims.Where(c => claims.Exists(x => x.Id == c.Id)).ToList();
            user.ClaimIds = user.Claims.Select(x => x.Id).ToList();
        }

        return users.Where(x => x.Claims.Count > 0).ToList();
    }

    public async Task<UserModel> GetUser(string email)
    {
        return _mapper.Map<UserModel>(await _userRepository.GetByEmail(email));
    }
    public async Task<UserModel> GetCompanyUser(string email, int companyId)
    {
        return _mapper.Map<UserModel>(await _userRepository.GetCompanyUserByEmailAsync(email, companyId));
    }

    public async Task<UserModel> GetInactiveCompanyUser(string email)
    {
        return _mapper.Map<UserModel>(await _userRepository.GetInactiveCompanyUserByEmailAsync(email));
    }


    private async Task<List<Claim>> GetClaimEntitiesAsync(ClaimsPrincipal claimsPrincipal)
    {
        var claims = await _claimsRepository.GetAllAsync();

        return claims.Where(c => c.ClaimType != ClaimTypes.Admin
                                 || claimsPrincipal.HasClaim(x => x.Type == c.Key)
                                 || claimsPrincipal.IsSuperAdmin()
        ).ToList();
    }

    public async Task<List<ClaimModel>> GetClaimsAsync(ClaimsPrincipal claimsPrincipal)
    {
        var entities = await GetClaimEntitiesAsync(claimsPrincipal);

        entities = entities
            .Where(e => e.ClaimType != ClaimTypes.Company)
            .ToList();

        return _mapper.Map<List<ClaimModel>>(entities);
    }

    public async Task<ClaimModel> GetClaimByNameAsync(string name)
    {
        var claim = await _claimsRepository.GetByNameAsync(name);
        return _mapper.Map<ClaimModel>(claim);
    }

    public async Task<List<AssessorModel>> GetAssessorsAsync()
    {
        return _mapper.Map<List<AssessorModel>>(await _userRepository.GetAssessorsAsync());
    }

    public async Task<AssessorModel> GetAssessorAsync(int id)
    {
        return _mapper.Map<AssessorModel>(await _userRepository.GetAssessorAsync(id));
    }

    public async Task UpdateAssessor(int id, IEnumerable<int> selectedSubstanceIds, int qualityCheckPercentage)
    {
        ValidateQualityCheckPercentage(qualityCheckPercentage);
        var user = await _userRepository.GetById(id);
        if (user == null)
        {
            throw new NotFoundException("User not found");
        }
        var substanceIdsToAdd = selectedSubstanceIds.Where(c => user.UserSubstances.All(x => x.SubstanceId != c));
        var userSubstancesToRemove =
            user.UserSubstances.Where(us => !selectedSubstanceIds.Contains(us.SubstanceId));
        await _userRepository.UpdateAssessorAsync(user, substanceIdsToAdd, userSubstancesToRemove,
            qualityCheckPercentage);

    }

    public async Task RecordLogin(string email)
    {
        var user = await _userRepository.GetByEmail(email);
        user.UpdateLastLoginDate();
        await _userRepository.SaveChangesAsync();
    }

    public async Task<IEnumerable<UserLocksModel>> GetUserLocks()
    {
        return await _userRepository.GetUserLocks();
    }

    private static void ValidateQualityCheckPercentage(int qualityCheckPercentage)
    {
        var qualityCheckPercentageList = Enumerable.Range(0, (100 - 10) / 5 + 1)
            .Select(i => 10 + i * 5);

        if (!qualityCheckPercentageList.Contains(qualityCheckPercentage))
        {
            throw new ArgumentException("Invalid quality check percentage");
        }
    }
}