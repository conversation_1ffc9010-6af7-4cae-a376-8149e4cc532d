using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Enums;

namespace PharmaLex.VigiLit.ImportManagement.Service.Repositories;

internal class ImportRepository : TrackingGenericRepository<Import>,  IImportingImportRepository
{
    protected readonly IMapper _mapper;

    public ImportRepository(PlxDbContext context, IMapper mapper, IUserContext userContext) : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<Import?> GetById(int id)
    {
        return await context.Set<Import>()
            .Where(i => i.Id == id)
            .FirstOrDefaultAsync();
    }

    public async Task<Import?> GetByIdForImport(int id)
    {
        return await context.Set<Import>()
            .Include(i => i.ImportContracts)
            .Where(i => i.Id == id)
            .FirstOrDefaultAsync();
    }

    public async Task<Import?> GetPriorityImportForProcessing()
    {
        var query = context.Set<Import>()
            .Include(i => i.ImportContracts)
            .Where(i => i.ImportStatusType == ImportStatusType.Queued || i.ImportStatusType == ImportStatusType.Started)
            .OrderBy(i => i.ImportType)
            .ThenBy(i => i.Id)
            .AsNoTracking();

        return await query.FirstOrDefaultAsync();
    }

    public async Task Archive(int importId)
    {
        var import = await context.Set<Import>()
            .Where(i => i.Id == importId)
            .FirstAsync();

        import.ImportDashboardStatusType = ImportDashboardStatusType.Archived;

        await context.SaveChangesAsync();
    }
}
