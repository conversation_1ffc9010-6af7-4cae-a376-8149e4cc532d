﻿@using PharmaLex.Core.Web.Helpers
@model List<PharmaLex.VigiLit.ImportManagement.Ui.FileImport.ImportFileModel>
@Html.AntiForgeryToken()

<div class="sub-header">
    <h2>File Import</h2>
</div>

<div id="file-import-container" v-cloak>
    <section>
        <div>
            <div id="drop-zone"></div>
            <div id="upload-progress"></div>

            <table v-if="uploadedFiles.length > 0" class="filename-table">
                <thead>
                    <tr>
                        <th>Uploaded Files</th>
                        <th>Size</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="file in uploadedFiles" :key="file.name">
                        <td>{{ file.fileName }}</td>
                        <td>{{formatSize(file.fileSize)}} </td>
                        <td> <a @@click="onDelete(file)"><i class="m-icon">delete</i> </a> </td>
                        <td> <a @@click="onDownload(file)"><i class="m-icon">download</i> </a></td>
                    </tr>

                </tbody>
            </table>
        </div>

        <div class="button-container">
            <a v-if="!isEdit" class="button secondary icon-button-cancel btn-default" @@click="onCancel">Cancel</a>
            <a v-if="isEdit && !hasDiscardableFiles " class="button secondary icon-button-cancel btn-default" :href="'/Import/AdHocList'">Close</a>
            <a v-if="isEdit && hasDiscardableFiles" class="button secondary icon-button-cancel btn-default" @@click="onDiscard">Discard</a>
            <a class="button" @@click="onCreate">{{ saveOrCreateBtnLabel }}</a>


        </div>
    </section>
</div>
@section Scripts {

    <script src="@Cdn.GetUrl("lib/uppy/uppy.min.js")"></script>

    <script type="text/javascript">

        const token = document.getElementsByName("__RequestVerificationToken")[0].value;

        var model = @Html.Raw(AntiXss.ToJson(Model));
        var isEdit = model && model.length > 0;
        // If its Edit get the batchId from model else if its Create then set random guid for batchId
        const guid = isEdit ? model[0].batchId : crypto.randomUUID();

        // This sets up VueJS differently to the rest of the app (which uses _Layout.cshtml) so get Uppy to work alongside VueJS
        const { createApp } = Vue;
        const app = createApp({
            data() {
                return {
                    message: "Drag drop here",
                    uploadedFiles: isEdit ? model : [],
                    isEdit: isEdit,
                    hasDiscardableFiles: false
                };
            },
            methods: {
                onCreate() {
                    if (this.uploadedFiles.length > 0) {
                        location.href = `/Import/AdHocList`;
                    } else {

                        return plx.toast.show('Please upload files', 2, 'failed', null, 1500);
                    }
                },
                onDelete(file) {
                        fetch(`/Import/DeleteImportFile`, {
                            method: 'DELETE',
                            credentials: 'same-origin',
                            body: JSON.stringify(file),
                            headers: {
                                'Content-Type': 'application/json',
                                "RequestVerificationToken": token
                            }
                        }).then(result => {
                            if (result.ok) {
                                plx.toast.show(`File "${file.fileName}" deleted successfully`, 5, 'confirm', null, 2500);

                                if (this.uploadedFiles.length > 0) {
                                    this.uploadedFiles = this.uploadedFiles.filter(f => f !== file);
                                }
                            }
                            else {
                                return plx.toast.show('Deleting files failed.', 2, 'failed', null, 1000);
                            }

                        });
                },
                onDownload(file) {
                    
                    let url = `/Import/DownloadImportedFile/${file.batchId}/${file.fileName}`;

                    var onFailure = function () {
                        plx.toast.show('Downloading files failed.', 2, 'failed', null, 1000);
                    }
                    var onComplete = function () {
                        plx.toast.show(`File "${file.fileName}" downloaded successfully`, 5, 'confirm', null, 2500);
                    }
                    DownloadFile.fromUrl(url, null, null, onFailure, onComplete);
                },
                onDiscard() {
                    var discardableFiles = this.getDiscardableFiles(this.uploadedFiles);
                    fetch(`/Import/DeleteImportFiles`, {
                        method: 'DELETE',
                        credentials: 'same-origin',
                        body: JSON.stringify(discardableFiles),
                        headers: {
                            'Content-Type': 'application/json', 
                            "RequestVerificationToken": token
                        }
                    }).then(result => {
                        if (result.ok) {
                            plx.toast.show(`Files discarded successfully`, 5, 'confirm', null, 2500);

                            if (this.uploadedFiles.length > 0) {
                                this.uploadedFiles = this.uploadedFiles.filter(file => !discardableFiles.includes(file));
                                this.hasDiscardableFiles = false;
                            }
                        }
                        else {
                            return plx.toast.show('Deleting files failed.', 2, 'failed', null, 1000);
                        }
                    });
                },
                onCancel() {
                    if (this.uploadedFiles.length > 0) {
                        fetch(`/Import/DeleteImportFilesBatch?batchId=${guid}`, {
                            method: 'DELETE',
                            credentials: 'same-origin',
                            headers: {
                                'Content-Type': 'application/json',
                                "RequestVerificationToken": token
                            }
                        }).then(result => {
                            if (result.ok) {
                                location.href = `/Import/AdHocList`;
                            }
                            else {
                                return plx.toast.show('Deleting files failed.', 2, 'failed', null, 1000);
                            }

                        });
                    } else {
                        location.href = `/Import/AdHocList`;

                    }
                },
                formatSize(fileSize) {
                    return fileSize === 0 ? '' : `${(fileSize / 1024 / 1024).toFixed(2)} MB`;
                },
                getDiscardableFiles(uploadedFiles) {
                    return uploadedFiles.filter(file => file.hasOwnProperty('isDiscardable') && file.isDiscardable === true);
                }
            },
            computed: {
                saveOrCreateBtnLabel() {
                    return this.isEdit ? "Save" : "Create";

                }
           
            }
        }).mount("#file-import-container");

        (function () {
            let uppy = Uppy.Core({
                debug: true,
                autoProceed: true,
                restrictions: {
                    maxNumberOfFiles: 10,
                    allowedFileTypes: ['application/pdf']
                }
            });
            uppy.use(Uppy.DragDrop, {
                target: '#drop-zone',
                width: '650px',
                height: '200px',
                note: ' file to upload (PDF only), up to 10MB each'
            })
                .use(Uppy.XHRUpload, {
                    endpoint: `/Import/FileImport?batchId=${guid}`,
                    formData: true,
                    fieldName: 'files',
                    timeout: 0,
                    method: "POST",
                    credentials: 'same-origin',
                    headers: {
                        "RequestVerificationToken": token
                    }
                })
                .use(Uppy.ProgressBar, {
                    target: '#upload-progress',
                    hideAfterFinish: true
                });
            uppy.on('upload-success', (file, resp) => {
                app.uploadedFiles.push({
                    fileName: file.name,
                    fileSize: file.size,
                    batchId: guid,
                    isDiscardable: true
                });
                app.hasDiscardableFiles = app.getDiscardableFiles(app.uploadedFiles).length > 0;
                plx.toast.show('File uploaded Successfully', 5, 'confirm', null, 2500);
            });
            uppy.on('upload-error', (file, error, response) => {
                plx.toast.show('An error occurred importing the file. Please try again.', 5, 'failed', null, 2500);
            });
            uppy.on('complete', (result) => {
                const conflictFiles = result.failed.filter(file => file.response?.status === 409)
                if (conflictFiles.length > 0) {
                    const fileNames = conflictFiles.map(file => `"${file.name}"`).join(', ')
                    plx.toast.show(`The following files already exist: ${fileNames}`, 5, 'failed', null, 4000)
                }
            });
        })();
    </script>
}