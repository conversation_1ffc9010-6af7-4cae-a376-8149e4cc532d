﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.ImportManagement.Enums;
using System.Collections.Generic;

namespace PharmaLex.VigiLit.Domain.Models;

public class Import : VigiLitEntityBase
{
    public ImportType ImportType { get; set; }
    public ImportTriggerType ImportTriggerType { get; set; }

    public ImportDashboardStatusType ImportDashboardStatusType { get; set; }

    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }

    public ImportStatusType ImportStatusType { get; set; }

    public DateTime? ImportDate { get; set; }

    public ICollection<ImportContract> ImportContracts { get; set; } = new List<ImportContract>();

    public Import()
    {
    }

    public Import(ImportType type, ImportTriggerType triggerType, DateTime? importDate)
    {
        ImportType = type;
        ImportTriggerType = triggerType;
        ImportStatusType = ImportStatusType.Queued;
        ImportDate = importDate;
    }

    public void SetProcessingFields(DateTime startDate, DateTime endDate, ImportStatusType status)
    {
        StartDate = startDate;
        EndDate = endDate;
        ImportStatusType = status;
    }

    public void StartProcessing()
    {
        if (ImportStatusType == ImportStatusType.Queued)
        {
            StartDate = DateTime.UtcNow;
            ImportStatusType = ImportStatusType.Started;
        }
    }

    public void EndProcessing(ImportStatusType status)
    {
        // don't overwrite during a retry
        if (!EndDate.HasValue)
        {
            EndDate = DateTime.UtcNow;
        }

        ImportStatusType = status;
        ImportDashboardStatusType = ImportDashboardStatusType.Imported;
    }
    public bool HasNoImportContracts()
    {
        return ImportContracts.Count == 0;
    }

    public static Import CreateCompletedImport(TimeProvider timeProvider, ImportType importType)
    {
        var import = new Import(importType, ImportTriggerType.Manual, DateTime.UtcNow.Date)
        {
            ImportStatusType = ImportStatusType.Completed,
            StartDate = timeProvider.GetUtcNow().UtcDateTime,
            EndDate = timeProvider.GetUtcNow().UtcDateTime,
            ImportDashboardStatusType = ImportDashboardStatusType.Imported,
        };
        return import;
    }
}
