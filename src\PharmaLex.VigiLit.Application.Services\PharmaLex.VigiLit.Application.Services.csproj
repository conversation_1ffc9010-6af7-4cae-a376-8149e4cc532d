﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.Core.UserSessionManagement.Entities\PharmaLex.Core.UserSessionManagement.Entities.csproj" />
    <ProjectReference Include="..\PharmaLex.Core.UserSessionManagement\PharmaLex.Core.UserSessionManagement.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.AccessControl\PharmaLex.VigiLit.AccessControl.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.AiAnalysis.Client\PharmaLex.VigiLit.AiAnalysis.Client.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.AiAnalysis.Entities\PharmaLex.VigiLit.AiAnalysis.Entities.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Application\PharmaLex.VigiLit.Application.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Company\PharmaLex.VigiLit.Company.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ContractManagement\PharmaLex.VigiLit.ContractManagement.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Domain.Interfaces\PharmaLex.VigiLit.Domain.Interfaces.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Contracts\PharmaLex.VigiLit.ImportManagement.Contracts.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Logging\PharmaLex.VigiLit.Logging.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ReferenceManagement.Contracts\PharmaLex.VigiLit.ReferenceManagement.Contracts.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Ui.ViewModels\PharmaLex.VigiLit.Ui.ViewModels.csproj" />
  </ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.Application.Services.Unit.Tests"></InternalsVisibleTo>
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Include="Microsoft.FeatureManagement.AspNetCore" Version="3.5.0" />
	  <PackageReference Include="NewRelic.Agent.Api" Version="10.41.0" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Comparers\" />
	</ItemGroup>

</Project>
