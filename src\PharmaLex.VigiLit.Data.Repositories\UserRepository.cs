﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.Core.UserManagement.Users;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

namespace PharmaLex.VigiLit.Data.Repositories;

public class UserRepository : TrackingGenericRepository<User>, IUserRepository
{
    private readonly IMapper _mapper;

    public UserRepository(PlxDbContext context, IMapper mapper, IUserContext userContext) : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<IEnumerable<UserFullModel>> GetAllAsync(bool shouldHaveClaims = true)
    {
        var query = context.Set<User>()
            .Include(u => u.ClaimsInternal)
            .Where(u => !shouldHaveClaims || u.ClaimsInternal.Any())
            .AsNoTracking();

        return await _mapper.ProjectTo<UserFullModel>(query).ToListAsync();
    }

    public Task<User?> GetByEmail(string email)
    {
        return context.Set<User>()
            .Include(u => u.ClaimsInternal)
            .Include(u => u.UserSubstances)
         .FirstOrDefaultAsync(u => u.Email == email);
    }

    public Task<User?> GetCompanyUserByEmailAsync(string email, int companyId)
    {
        return context.Set<User>()
            .Include(u => u.ClaimsInternal)
            .Include(u => u.CompanyUser)
            .Include(u => u.UserSubstances)
            .FirstOrDefaultAsync(u => u.Email == email && u.CompanyUser.CompanyId == companyId);

    }

    public Task<User?> GetInactiveCompanyUserByEmailAsync(string email)
    {
        return context.Set<User>()
            .Include(u => u.ClaimsInternal)
            .Include(u => u.CompanyUser)
            .FirstOrDefaultAsync(u => u.Email == email && !u.CompanyUser.Active);
    }

    public Task<User?> GetById(int id)
    {
        return context.Set<User>()
            .Include(u => u.CompanyUser)
            .Include(u => u.ClaimsInternal)
            .Include(u => u.UserSubstances)
            .Include(u => u.UserEmailPreferences)
            .FirstOrDefaultAsync(u => u.Id == id);
    }

    public async Task<IEnumerable<User>> GetAssessorsAsync()
    {
        return await context.Set<User>()
            .Include(u => u.ClaimsInternal)
            .Include(u => u.UserSubstances)
            .Where(u => u.ClaimsInternal.Any(c => c.Name == Claims.PreAssessor))
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<User?> GetAssessorAsync(int id)
    {
        return await context.Set<User>()
            .Include(u => u.ClaimsInternal)
            .Include(u => u.UserSubstances)
            .Where(u => u.ClaimsInternal.Any(c => c.Name == Claims.PreAssessor))
            .FirstOrDefaultAsync(u => u.Id == id);
    }

    public async Task UpdateAssessorAsync(User user, IEnumerable<int> substanceIdsToAdd, IEnumerable<UserSubstance> userSubstancesToRemove, int qualityCheckPercentage)
    {
        foreach (var substanceId in substanceIdsToAdd)
        {
            UserSubstance userSubstance = new(user.Id, substanceId);
            user.UserSubstances.Add(userSubstance);
        }

        foreach (var userSubstance in userSubstancesToRemove)
        {
            user.UserSubstances.Remove(userSubstance);
        }

        user.QCPercentage = qualityCheckPercentage;

        await SaveChangesAsync();
    }

    public async Task<IEnumerable<UserLocksModel>> GetUserLocks()
    {
        var query = context.Set<User>()
            .Include(u => u.ReferenceClassificationLocks)
            .Where(u => u.ReferenceClassificationLocks.Any())
            .AsNoTracking()
            .OrderBy(u => u.Id);

        return await _mapper.ProjectTo<UserLocksModel>(query).ToListAsync();
    }

    public Task<User?> GetForSecurity(int userId)
    {
        return context.Set<User>()
            .Include(u => u.CompanyUser)
            .Include(u => u.ClaimsInternal)
            .AsNoTracking()
            .FirstOrDefaultAsync(u => u.Id == userId);
    }

    public async Task<IEnumerable<UserFindResult>> GetAllUsers(bool shouldHaveClaims = true)
    {
        var query = context.Set<User>()
            .Include(u => u.ClaimsInternal)
            .Where(u => !shouldHaveClaims || u.ClaimsInternal.Any())
            .AsNoTracking();

        return await _mapper.ProjectTo<UserFindResult>(query).ToListAsync(); 
    }
}
