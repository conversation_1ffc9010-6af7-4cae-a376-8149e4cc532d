﻿using PharmaLex.Core.UserManagement.Claims;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace PharmaLex.Core.UserManagement.Ui;

public class UserViewModel
{
    public int Id { get; set; }
    [Required, StringLength(256)]
    [EmailAddress(ErrorMessage = "Invalid Email Address.")]
    public string Email { get; set; } = string.Empty;

    [Required, StringLength(512)] 
    public string GivenName { get; set; } = string.Empty;
    [Required, StringLength(512)] 
    public string FamilyName { get; set; } = string.Empty;
    public DateTime? LastLoginDate { get; set; }
    public string DisplayFullName => $"{GivenName} {FamilyName}";
    public string DisplayLastLoginDate => LastLoginDate.HasValue ? LastLoginDate.Value.ToString("dd MMMM yyyy HH:mm") : string.Empty;

    public virtual List<int> ClaimIds { get; set; } = [];

    [JsonIgnore]

    public List<ClaimModel> Claims { get; set; } = [];
}
