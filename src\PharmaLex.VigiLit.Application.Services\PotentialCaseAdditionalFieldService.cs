﻿using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using AutoMapper;
using PharmaLex.VigiLit.Domain.Enums;

namespace PharmaLex.VigiLit.Application.Services;

public class PotentialCaseAdditionalFieldService(IPotentialCaseAdditionalFieldRepository potentialCaseAdditionalFieldRepository,
                                                 IClassificationCategoryService classificationCategoryService,
                                                 IMapper mapper)
                                                 : IPotentialCaseAdditionalFieldService
{
    public async Task<IEnumerable<PotentialCaseAdditionalFieldModel>> GetAllAsync()
    {
        var res = await potentialCaseAdditionalFieldRepository.GetAllAsync();
        return mapper.Map< IEnumerable<PotentialCaseAdditionalFieldModel>>(res);
    }

    public async Task SetPotentialCaseAdditionalInformation(PrintPreviewPageModel model)
    {
        var potentialCaseAdditionalInformation = await potentialCaseAdditionalFieldRepository.GetAllAsync();
        var records = mapper.Map<IEnumerable<PotentialCaseAdditionalFieldModel>>(potentialCaseAdditionalInformation).ToList();
        var potentialCaseCategory = await classificationCategoryService.GetByIdAsync(1);

        foreach (var classification in model.Classifications)
        {
            if (classification.ClassificationCategory == potentialCaseCategory.Name)
            {
                var integers = SplitBracketedNumbers(classification.PotentialCaseAdditionalInformation);
                if (integers.Count > 0)
                {
                    classification.PotentialCaseAdditionalInformationList = records.Where(x => integers.Contains(x.Id)).Select(x => x.Name).ToList();
                }
            }
        }
    }

    private static List<int> SplitBracketedNumbers(string input)
    {
        var result = new List<int>();

        if (!string.IsNullOrEmpty(input))
        {
            foreach (var part in input.Split(']'))
            {
                if (string.IsNullOrWhiteSpace(part)) continue;

                string numberPart = part.TrimStart('[');
                if (int.TryParse(numberPart, out int number))
                {
                    result.Add(number);
                }
            }
        }

        return result;
    }
}