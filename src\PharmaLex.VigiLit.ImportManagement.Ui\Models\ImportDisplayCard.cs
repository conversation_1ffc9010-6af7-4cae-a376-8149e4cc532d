﻿namespace PharmaLex.VigiLit.ImportManagement.Ui.Models;
public class ImportDisplayCard
{
    public required string Id { get; set; }
    public required string ImportType { get; set; }
    public required string Title { get; set; }
    public int NumberOfContracts { get; set; }
    public required string DateFrom { get; set; }
    public required string DateTo { get; set; }
    public int FileCount { get; set; }
    public List<string> Files { get; set; } = [];
    public int FilesPlus { get; set; }
    public required string CreatedBy { get; set; }
    public required string LastUpdatedBy { get; set; }
    public required string Filename { get; set; }
    public required int FailedImportStatus { get; set; }

}