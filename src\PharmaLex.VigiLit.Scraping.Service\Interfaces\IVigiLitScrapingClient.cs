using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Scraping.Service.Interfaces;

public interface IVigiLitScrapingClient
{
    /// <summary>
    /// Restores schedules in Apify for existing journals by grouping them by schedule (CronExpression)
    /// and creating corresponding schedules, tasks, and webhooks in Apify.
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns>A summary of the restoration process including number of schedules created</returns>
    Task<RestoreSchedulesResult> RestoreSchedulesAsync(CancellationToken cancellationToken = default);
}

public class RestoreSchedulesResult
{
    public int SchedulesCreated { get; set; }
    public int TasksCreated { get; set; }
    public int WebhooksCreated { get; set; }
    public int JournalsProcessed { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Messages { get; set; } = new();
}
