﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddFeatureFlagEnableFileImportManualEntry : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("SET IDENTITY_INSERT [dbo].[FeatureFlags] ON; " +

                                 "INSERT INTO [dbo].[FeatureFlags] ( [Id], [Name], [Enabled], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT 4, 'EnableFileImportManualEntry', 1, GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script' " +
                                 "WHERE NOT EXISTS (SELECT [Id] FROM [dbo].[FeatureFlags] WHERE [Id] = 4); " +

                                 "SET IDENTITY_INSERT [dbo].[FeatureFlags] OFF; ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DELETE FROM [FeatureFlags] WHERE Id = 4;");
        }
    }
}
