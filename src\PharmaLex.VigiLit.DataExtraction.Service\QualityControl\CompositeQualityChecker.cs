﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;

namespace PharmaLex.VigiLit.DataExtraction.Service.QualityControl;

internal class CompositeQualityChecker : ICompositeQualityChecker
{
    private readonly IEnumerable<IExtractionValidator> _extractionValidators;
    private readonly ILogger<CompositeQualityChecker> _logger;
    
    public CompositeQualityChecker(ILogger<CompositeQualityChecker> logger, IEnumerable<IExtractionValidator> extractionValidators)
    {
        _logger = logger;
        _extractionValidators = extractionValidators;
    }

    public bool IsValid(ExtractedReference extractedReference)
    {
       return _extractionValidators.All(v=>
       {
            var result = v.IsValid(extractedReference);
            if (!result)
            {
                _logger.LogWarning("Validation failed in: {QualityCheck}", v.GetType().Name);
            }
            return result;
       });
    }
}