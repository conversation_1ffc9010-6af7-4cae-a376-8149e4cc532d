﻿using PharmaLex.Core.UserManagement.Users;
using PharmaLex.DataAccess;

namespace PharmaLex.Core.UserManagement;

public interface IUserRepository<TUserEntity>
    where TUserEntity : EntityBase, IUserEntity
{
    /// <summary>
    /// Asynchronously retrieves all users.
    /// </summary>
    /// <param name="shouldHaveClaims">Optional parameter indicating whether users should include their claims.</param>
    /// <returns>A collection of user models.</returns>
    Task<IEnumerable<UserFindResult>> GetAllUsers(bool shouldHaveClaims = true);

    /// <summary>
    /// Asynchronously retrieves a user by their ID.
    /// </summary>
    /// <param name="id">The ID of the user to retrieve.</param>
    /// <returns>The user with the specified ID, if found; otherwise, null.</returns>
    Task<TUserEntity?> GetById(int id);

    /// <summary>
    /// Asynchronously retrieves a user by their email address.
    /// </summary>
    /// <param name="email">The email address of the user to retrieve.</param>
    /// <returns>The user with the specified email address, if found; otherwise, null.</returns>
    Task<TUserEntity?> GetByEmail(string email);

    /// <summary>
    /// Asynchronously retrieves a user for security purposes.
    /// </summary>
    /// <param name="userId">The ID of the user to retrieve.</param>
    /// <returns>The user with the specified ID, if found; otherwise, null.</returns>
    Task<TUserEntity?> GetForSecurity(int userId);
}