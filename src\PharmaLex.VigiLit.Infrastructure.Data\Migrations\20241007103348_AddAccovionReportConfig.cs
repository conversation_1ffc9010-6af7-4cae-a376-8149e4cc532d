﻿using Microsoft.EntityFrameworkCore.Migrations;
using PharmaLex.VigiLit.Infrastructure.Data.Extensions;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddAccovionReportConfig : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.SqlFileExec("spCaseFileReport.sql");

            migrationBuilder.Sql(@"
            DECLARE @companyName2 varchar(20) = 'Accovion GmbH';
            DECLARE @companyId2 int;
            DECLARE @reportDescription2 varchar(25) = 'Case file reports';
            DECLARE @reportId2 int;
            DECLARE @superAdminClaimId2 int;
            DECLARE @internalSupportClaimId2 int;
            DECLARE @clientResearcherClaimId2 int;
            -- Company
            IF NOT EXISTS(SELECT * FROM [dbo].[Companies] WHERE Name = @companyName2)
            BEGIN
	            INSERT INTO [dbo].[Companies]
	            ([Name],[ContactPersonName],[ContactPersonEmail],[IsActive],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy])
	            VALUES	(@companyName2,'','',1,GetUTCdate(),'Script',GetUTCdate(),'Script')
            END
            SELECT @companyId2=Id FROM [dbo].[Companies] WHERE Name = @companyName2
            -- Reports
            IF NOT EXISTS(SELECT * FROM [dbo].[Reports] WHERE Name = @companyName2 AND [Description] = @reportDescription2)
            BEGIN
	            INSERT INTO [dbo].[Reports]	([Name],[Description],[AllCompanies],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy],[ControllerName])
	            VALUES (@companyName2,@reportDescription2,0,GetUTCdate(),'Script',GetUTCdate(),'Script','AccovionClientReport')
            END
            SELECT @reportId2=Id FROM [dbo].[Reports] WHERE Name = @companyName2 AND [Description] = @reportDescription2
            -- Report claims
            SELECT @superAdminClaimId2=Id from [dbo].[Claims] WHERE Name = 'SuperAdmin'
            SELECT @internalSupportClaimId2=Id from [dbo].[Claims] WHERE Name = 'InternalSupport'
            SELECT @clientResearcherClaimId2=Id from [dbo].[Claims] WHERE Name = 'ClientResearcher'
            IF NOT EXISTS(SELECT * FROM [dbo].ReportClaims WHERE ReportId = @reportId2 AND ClaimId = @superAdminClaimId2)
            BEGIN
	            INSERT INTO [dbo].[ReportClaims]
	            ([ReportId],[ClaimId],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy])
	            VALUES
	            (@reportId2,@superAdminClaimId2,GetUTCdate(),'Script',GetUTCdate(),'Script')
            END
            IF NOT EXISTS(SELECT * FROM [dbo].ReportClaims WHERE ReportId = @reportId2 AND ClaimId = @internalSupportClaimId2)
            BEGIN
	            INSERT INTO [dbo].[ReportClaims]
	            ([ReportId],[ClaimId],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy])
	            VALUES
	            (@reportId2,@internalSupportClaimId2,GetUTCdate(),'Script',GetUTCdate(),'Script')
            END
            IF NOT EXISTS(SELECT * FROM [dbo].ReportClaims WHERE ReportId = @reportId2 AND ClaimId = @clientResearcherClaimId2)
            BEGIN
	            INSERT INTO [dbo].[ReportClaims]
	            ([ReportId],[ClaimId],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy])
	            VALUES
	            (@reportId2,@clientResearcherClaimId2,GetUTCdate(),'Script',GetUTCdate(),'Script')
            END
            -- Company reports
            IF NOT EXISTS(SELECT * FROM [CompanyReports] WHERE CompanyId=@companyId2 AND ReportId = @reportId2)
            BEGIN
	            INSERT INTO [dbo].[CompanyReports] ([CompanyId],[ReportId],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy])
	            VALUES	(@companyId2,@reportId2,GetUtcdate(),'script',GetUtcdate(),'script')
            END
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
