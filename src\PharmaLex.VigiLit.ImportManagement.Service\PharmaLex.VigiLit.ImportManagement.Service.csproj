﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
	  <InternalsVisibleTo Include="PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests"></InternalsVisibleTo>
	  <InternalsVisibleTo Include="PharmaLex.VigiLit.ImportManagement.Service.Integration.Tests"></InternalsVisibleTo>
	  <InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FuzzySharp" Version="2.0.2" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Features" Version="4.14.0" />
    <PackageReference Include="NewRelic.Agent.Api" Version="10.41.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.14.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Scripting" Version="4.14.0" />
    <PackageReference Include="PharmaLex.FeatureManagement" Version="*********" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.VigiLit.AiAnalysis.Client\PharmaLex.VigiLit.AiAnalysis.Client.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ContractManagement\PharmaLex.VigiLit.ContractManagement.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Client\PharmaLex.VigiLit.ImportManagement.Client.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Contracts\PharmaLex.VigiLit.ImportManagement.Contracts.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Entities\PharmaLex.VigiLit.ImportManagement.Entities.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Logging\PharmaLex.VigiLit.Logging.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ReferenceManagement.Contracts\PharmaLex.VigiLit.ReferenceManagement.Contracts.csproj" />
  </ItemGroup>

</Project>
