﻿using PharmaLex.VigiLit.ReferenceManagement;

namespace PharmaLex.VigiLit.ImportManagement.Client.Dto;

#pragma warning disable CS8618
public class ReferenceDto : IReference
{
    public string Abstract { get; set; }
    public string AffiliationTextFirstAuthor { get; set; }
    public string Authors { get; set; }
    public string CountryOfOccurrence { get; set; }
    public DateTime DateRevised { get; set; }
    public string Doi { get; set; }
    public string FullPagination { get; set; }
    public string Issn { get; set; }
    public string Issue { get; set; }
    public string Language { get; set; }
    public int SourceSystem { get; set; }
    public string SourceId { get; set; }
    public string PublicationType { get; set; }
    public ushort? PublicationYear { get; set; }
    public string Title { get; set; }
    public string Volume { get; set; }
    public string VolumeAbbreviation { get; set; }
    public string Keywords { get; set; }
    public string MeshHeadings { get; set; }
    public string JournalTitle { get; set; }
    public string? DocumentLocation { get; set; }
}
#pragma warning restore CS8618