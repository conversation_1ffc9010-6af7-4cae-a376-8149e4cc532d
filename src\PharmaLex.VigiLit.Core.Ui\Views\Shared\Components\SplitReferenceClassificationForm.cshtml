﻿<script type="text/x-template" id="split-reference-classification-form-template">

    <div>

        <h2>{{header}}</h2>

        <div class="form-group">
            <label :for="`substance_${referenceClassification.referenceId}`">Substance</label>
            <select :id="`substance_${referenceClassification.referenceId}`" v-model="userSelected.substanceIdSelected"  @@change="substanceChanged">
                <option value="">Select Substance</option>
                <option v-for="substance in substances" :value="substance.id" >
                    {{substance.name}}
                </option>
            </select>
        </div>

        <div class="form-group company-multi-select">
            <label>Client(s)</label>
            <multi-select :item-list="companies"
                          :placeholder="'Select Client(s)'" style="width: 100%"
                          :selections="userSelected.companyIds">
            </multi-select>
        </div>

        <div class="form-group">
            <label :for="`refclassification_${referenceClassification.referenceId}`">Classification Category</label>
            <select :id="`refclassification_${referenceClassification.referenceId}`" v-model="userSelected.referenceClassification.classificationCategoryId">
                <option value="0" disabled selected hidden>Select Classification Category</option>
                <option v-for="category in classificationCategories" :value="category.id" >
                    {{category.name}}
                </option>
            </select>
        </div>

        <div class="form-group" v-if="displayMinimalCriteria">
            <label :for="`criteria_${referenceClassification.referenceId}`">Minimal Criteria</label>
            <select :id="`criteria_${referenceClassification.referenceId}`" v-model="userSelected.referenceClassification.minimalCriteria">
                <option value="" disabled selected hidden>Select Minimal Criteria</option>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
            </select>
        </div>

        <div class="form-group" v-if="displayCountryOfOccurrence">
            <label :for="`country_${referenceClassification.referenceId}`">Country of Occurrence</label>
            <select :id="`country_${referenceClassification.referenceId}`" v-model="userSelected.referenceClassification.countryOfOccurrence">
                <option value="" selected>Select Country of Occurrence</option>
                <option v-for="country in countries" :value="country">
                    {{country}}
                </option>
            </select>
        </div>

        <div class="form-group">
            <label :for="`dosageform_${referenceClassification.referenceId}`">Dosage Form</label>
            <div class="dosage-form">
                <div>
                    <input :id="`dosageform_${referenceClassification.referenceId}`" type="text" v-model="userSelected.referenceClassification.dosageForm" class="dosageForm" />
                </div>
                <div class="dosage-form-badges">
                    <span v-for="option in dosageOptions" :value="option" v-on:click="userSelected.referenceClassification.dosageForm = option">{{option}}</span>
                </div>
            </div>
        </div>

        <div class="form-group" v-if="displayCountryOfOccurrence">
            <input :id="`countryverified_${referenceClassification.referenceId}`" type="checkbox" value="true" v-model="countryOfOccurrenceVerified" />
            <label :for="`countryverified_${referenceClassification.referenceId}`">Country of Occurrence Verified</label>
        </div>

        <br />
    </div>

</script>
<script type="text/javascript">

    const dosages = [
        "N.A",
        "N.I",
        "IN VITRO",
        "I.V",
        "IN VIVO",
        "P.O"
    ];

    vueApp.component('split-reference-classification-form', {
        template: '#split-reference-classification-form-template',
        props: {
            classificationCategories: Array,
            substances: Array,
            referenceClassification: Object,
            countries: Array,
            header: String,
            isCountryOfOccurrenceVerified: Boolean
        },
        emits: ['valid'],
        data: function() {
            return {
                dosageOptions: dosages,
                countryOfOccurrenceVerified: false,
                companies: [],
                userSelected: {
                    isValid: false,
                    companyIds: { selectedIds: [] },
                    substanceIdSelected: '',
                    referenceClassification: {
                        substanceId: '',
                        dosageForm: '',
                        minimalCriteria: this.referenceClassification.minimalCriteria,
                        countryOfOccurrence: this.referenceClassification.countryOfOccurrence,
                        classificationCategoryId: this.referenceClassification.classificationCategoryId
                    }
                }
            };
        },
        methods: {
            emitValid: function() {
                this.userSelected.isValid = this.isValidClassification;
                this.userSelected.referenceClassification.substanceId = this.userSelected.substanceIdSelected;

                this.$emit('valid', this.userSelected);
            },
            substanceChanged: function () {

                this.SetSelectionDefaults();

                let url = `/References/Split/Companies/${this.userSelected.substanceIdSelected}`;

                fetch(url, {
                    method: "GET",
                    credentials: 'same-origin',
                    headers: {
                        "Content-Type": "application/json",
                        "RequestVerificationToken": token
                    }
                })
                    .then(res => {
                        if (!res.ok) {
                            throw res;
                        }
                        return res.json();
                    })
                    .then(data => {
                        this.companies.length = 0;
                        this.companies = [...data];
                    });
            },
            SetSelectionDefaults() {
                this.userSelected.companyIds.selectedIds.length = 0;
                this.countryOfOccurrenceVerified = false;
                this.userSelected.referenceClassification = {
                    isValid: false,
                    substanceId: '',
                    dosageForm: '',
                    minimalCriteria: this.referenceClassification.minimalCriteria,
                    countryOfOccurrence: this.referenceClassification.countryOfOccurrence,
                    classificationCategoryId: this.referenceClassification.classificationCategoryId,
                }
            }
        },
        computed: {
            isValidClassification() {
                return this.userSelected.referenceClassification.classificationCategoryId > 0
                    && this.userSelected.substanceIdSelected > 0
                    && this.userSelected.companyIds.selectedIds.length > 0
                    && (!this.displayMinimalCriteria || !!this.userSelected.referenceClassification.minimalCriteria)
                    && (!this.displayCountryOfOccurrence || (!!this.userSelected.referenceClassification.countryOfOccurrence && this.countryOfOccurrenceVerified))
                    && (!!this.userSelected.referenceClassification.dosageForm);
            },
            isPotentialCase() {
                return this.userSelected.referenceClassification.classificationCategoryId == 1;
            },
            displayCountryOfOccurrence() {
                return this.isPotentialCase;
            },
            displayMinimalCriteria() {
                return this.isPotentialCase;
            },
        },
        created() {
            this.userSelected.referenceClassification.countryOfOccurrence = this.referenceClassification.countryOfOccurrence ? this.referenceClassification.countryOfOccurrence : '';
        },
        watch: {
            "userSelected.referenceClassification.classificationCategoryId"(newValue) {
                this.referenceClassification.countryOfOccurrence = newValue == 1 ? this.referenceClassification.countryOfOccurrence : null;
                this.userSelected.isValid = this.isValidClassification;
                this.emitValid();
            },
            "userSelected.referenceClassification.minimalCriteria"(newValue) {
                this.userSelected.isValid = this.isValidClassification;
                this.emitValid();
            },
            "userSelected.referenceClassification.countryOfOccurrence"(newValue) {
                this.userSelected.isValid = this.isValidClassification;
                this.emitValid();
            },
            "userSelected.referenceClassification.dosageForm"(newValue) {
                this.userSelected.isValid = this.isValidClassification;
                this.emitValid();
            },
            "userSelected.substanceIdSelected"(newValue) {
                this.userSelected.isValid = this.isValidClassification;
                this.emitValid();
            },
            "userSelected.companyIds.selectedIds.length"(newValue) {
                this.userSelected.isValid = this.isValidClassification;
                this.emitValid();
            },
            countryOfOccurrenceVerified: {
                handler(newValue) {
                    this.userSelected.isValid = this.isValidClassification;
                    this.emitValid();
                },
                immediate: true
            }
        }
    });
</script>

@section VueComponentScripts {
    <partial name="Components/MultiSelect" />
}
