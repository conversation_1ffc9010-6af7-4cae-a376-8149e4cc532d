﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Entities;
using PharmaLex.VigiLit.DataExtraction.Entities.Enums;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Service.QualityControl;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.ImportManagement.Client;
using PharmaLex.VigiLit.Logging;
using System.Text.Json;

namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;
internal class CallbackHandler : ICallbackHandler
{
    private readonly IMdeQueueItemRepository _repository;
    private readonly IImportManagementClient _importManagementClient;
    private readonly ILogger<CallbackHandler> _logger;
    private readonly IExtractDataFileDownload _extractDataFileDownload;
    private readonly float _mandatoryFieldMinimumConfidenceLevel;
    private readonly ICompositeQualityChecker _compositeQualityChecker;
    private readonly IDataExtractionProfile _extractionProfile;

    public CallbackHandler(ILogger<CallbackHandler> logger,
                                IImportManagementClient importManagementClient,
                                IExtractDataFileDownload fileDownload,
                                IMdeQueueItemRepository repository,
                                IConfiguration configuration,
                                ICompositeQualityChecker compositeQualityChecker, 
                                IDataExtractionProfile extractionProfile)
    {
        _logger = logger;
        _importManagementClient = importManagementClient;
        _extractDataFileDownload = fileDownload;
        _repository = repository;
        _compositeQualityChecker = compositeQualityChecker;
        _extractionProfile = extractionProfile;
        _mandatoryFieldMinimumConfidenceLevel = configuration.GetValue<float>("DataExtraction:MandatoryFieldMinimumConfidenceLevel");

    }

    public async Task<Stream> GetDocumentStream(Guid correlationId)
    {
        var queueItem = await _repository.GetByCorrelationId(correlationId);
        if (queueItem == null)
        {
            throw new ArgumentException($"Could not find item with correlation Id {correlationId}", nameof(correlationId));
        }

        var downloadFile = await _extractDataFileDownload.DownloadImportedFile(queueItem.BatchId, queueItem.Filename);

        queueItem.Status = MdeQueueItemStatus.Processing;
        await _repository.SaveChangesAsync();

        return new MemoryStream(downloadFile.Bytes);
    }

    public async Task Success(Guid correlationId, ExtractedMetadata extractedMetaData, ContextInfo contextInfo)
    {
        _logger.LogInformation("Received Success callback:{CorrelationId}", correlationId);
        var queueItem = await _repository.GetByCorrelationId(correlationId);
        if (queueItem == null)
        {
            throw new ArgumentException($"Could not find item with correlation Id {correlationId}", nameof(correlationId));
        }

        await CategoriseExtractedReferences(contextInfo, extractedMetaData, queueItem);

        queueItem.Status = MdeQueueItemStatus.Completed;
        await _repository.SaveChangesAsync();
    }

    public async Task Error(Guid correlationId)
    {
        _logger.LogInformation("Received Error callback:{CorrelationId}", correlationId);

        var queueItem = await _repository.GetByCorrelationId(correlationId);
        if (queueItem == null)
        {
            throw new ArgumentException($"Could not find item with correlation Id {correlationId}", nameof(correlationId));
        }

        queueItem.Status = MdeQueueItemStatus.Errored;
        await _repository.SaveChangesAsync();
    }

    private async Task CategoriseExtractedReferences(ContextInfo contextInfo, ExtractedMetadata extractedMetadata, MdeQueueItem queueItem)
    {
        var blobPathData = await _extractDataFileDownload.CreateTextBlobWithStorageMetaData(queueItem.BatchId, $"{Path.GetFileNameWithoutExtension(queueItem.Filename)}_TranslatedText.txt", contextInfo.RawTranslatedText);

        foreach (var extractedReference in extractedMetadata.Metadata)
        {
            if (DoesPassQualityControl(extractedReference))
            {
                await SendReference(contextInfo, extractedReference, queueItem.Source, blobPathData);
            }
            else
            {
                await SendFailedExtraction(contextInfo, extractedReference, queueItem);
            }
        }
    }

    private async Task SendReference(ContextInfo contextInfo, ExtractedReference extractedReference, Source source, BlobStoragePath storagePath)
    {
        var command = new EnqueueReferenceCommand()
        {
            Reference = _extractionProfile.Copy(extractedReference),
        };

        command.Reference.SourceSystem = MapExtractionSourceToDomainSource(source);
        command.Reference.Language = contextInfo.Language;
        command.Reference.DocumentLocation = JsonSerializer.Serialize(storagePath);

        _logger.LogInformation("SendReference:{ImportReferenceTitle}", LogSanitizer.Sanitize(command.Reference.Title));

        await _importManagementClient.Send(command);
    }

    private static int MapExtractionSourceToDomainSource(Source source)
    {
        switch (source)
        {
            case Source.File:
                return (int)SourceSystem.File; // Note coupling to Domain project, which is a wider issue around references and will need fixing.
            case Source.Web:
                return (int)SourceSystem.Web;
            default:
                throw new NotImplementedException($"Unknown source in DataExtraction: {source}");
        }
    }

    private async Task SendFailedExtraction(ContextInfo contextInfo, ExtractedReference extractedReference, MdeQueueItem queueItem)
    {
        try
        {
            var command = CreateManualCorrectionCommand(contextInfo, extractedReference, queueItem);

            _logger.LogInformation("SendFailedReference:{ImportReferenceTitle}", LogSanitizer.Sanitize(command.Reference.Title));

            await _importManagementClient.Send(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send failed extraction for correlation ID {CorrelationId}", queueItem.CorrelationId);
            throw new InvalidOperationException($"Failed to send failed extraction for correlation ID {queueItem.CorrelationId}", ex);
        }
    }

    private ManualCorrectionCommand CreateManualCorrectionCommand(ContextInfo contextInfo,
        ExtractedReference extractedReference,
        MdeQueueItem queueItem)
    {
        var failedImportReference = _extractionProfile.CopyFailed(extractedReference, _mandatoryFieldMinimumConfidenceLevel);
        failedImportReference.CorrelationId = queueItem.CorrelationId;
        failedImportReference.DocumentLocation = queueItem.DocumentLocation;
        failedImportReference.BatchId = queueItem.BatchId;
        failedImportReference.Filename = queueItem.Filename;
        failedImportReference.SourceSystem = (int)SourceSystem.ManualCorrection;
        failedImportReference.Language = contextInfo.Language;
        return new ManualCorrectionCommand { Reference = failedImportReference };
    }


    private bool DoesPassQualityControl(ExtractedReference extractedReference)
    {
        _logger.LogInformation("DoesPassQualityControl: {ExtractedReference}", LogSanitizer.Sanitize(extractedReference.Title.Value));
        var isPassing = _compositeQualityChecker.IsValid(extractedReference);
        _logger.LogInformation("DoesPassQualityControl: {IsPassing}", isPassing);
        return isPassing;
    }
}
