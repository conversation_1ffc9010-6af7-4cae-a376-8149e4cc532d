﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.Core.UserManagement\PharmaLex.Core.UserManagement.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.AccessControl\PharmaLex.VigiLit.AccessControl.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Domain.Interfaces\PharmaLex.VigiLit.Domain.Interfaces.csproj" />
  </ItemGroup>

  <ItemGroup>
	<InternalsVisibleTo Include="PharmaLex.VigiLit.ContractManagement.Unit.Tests"></InternalsVisibleTo>
  </ItemGroup>

</Project>
