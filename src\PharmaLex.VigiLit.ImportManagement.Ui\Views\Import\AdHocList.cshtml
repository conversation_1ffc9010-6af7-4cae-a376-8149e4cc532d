@model PharmaLex.VigiLit.ImportManagement.Ui.Models.ImportQueuePageModel
@using Microsoft.FeatureManagement
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.ContractManagement.Ui.Journals
@using PharmaLex.VigiLit.Domain.Interfaces.Services

@inject IFeatureManager FeatureManager
@{
    ViewData["Title"] = "Imports";
    const string enableManualEntry = "EnableManualEntry";
    const string enableFileImport = "EnableFileImportManualEntry";
    var isManualEntryFlagEnabled = await FeatureManager.IsEnabledAsync(enableManualEntry);
    var isFileImportFlagEnabled = await FeatureManager.IsEnabledAsync(enableFileImport);
}

@inject ICountryService _countryService
@{
    var _countries = await _countryService.GetAllAsync();
}

@inject IJournalService JournalService
@{
    var _journalTitles = await JournalService.GetNames();
}

<div id="importcards">
    @Html.AntiForgeryToken()
    <div id="ad-hoc-list">
        <div class="sub-header">
            <h2>Ad-Hoc Imports</h2>
            <div class="controls">
                <select id="selectAdHocImport" v-model="selectedAdHocImport" @@change="handleAdHocImportChange">
                    <option value="" selected disabled hidden>Create Ad-Hoc Import</option>
                    <option value="AdHocCreate">PUBMed</option>
                    @if (isManualEntryFlagEnabled)
                    {
                        <option value="ManualEntry">Manual Entry</option>
                    }
                </select>
            </div>
        </div>
    </div>
    <div>

        <ul class="sub-nav">
            <li>
                <a href="#" v-bind:class="{ active: filterType == 'All' }" @@click.prevent="this.filterType = 'All' ">All</a>
            </li>
            <li>
                <a href="#" v-bind:class="{ active: filterType == 'Pubmed' }" @@click.prevent="this.filterType = 'Pubmed' ">PubMed</a>
            </li>
            @if (isFileImportFlagEnabled)
            {
                <li>
                    <a href="#" v-bind:class="{ active: filterType == 'File' }" @@click.prevent="this.filterType = 'File' ">File</a>
                </li>
            }
            @if (isManualEntryFlagEnabled)
            {
                <li>
                    <a href="#" v-bind:class="{ active: filterType == 'Manual' }" @@click.prevent="this.filterType = 'Manual' ">Manual</a>
                </li>
            }
            @if (isFileImportFlagEnabled)
            {
                <li>
                    <a href="#" v-bind:class="{ active: filterType == 'Failed' }" @@click.prevent="this.filterType = 'Failed' ">Failed Imports</a>
                </li>
            }
        </ul>
    </div>
    <section>
        <div class="cards" v-if="filteredCards.length > 0">
            <div class="card" v-for="vgImport in filteredCards">
                <div v-if="vgImport.importType=='Pubmed'">
                    <div class="card-info" @@click.stop.prevent="viewImportDetails(vgImport)">
                        <div class="import-card-type">

                            <div class="import-type import-card-title-text adhoc">
                                {{vgImport.importType}} Import
                            </div>

                            <div class="ellipsis-container" @@click.stop>
                                <div class="ellipsis-button">
                                    <a :href="">
                                        <span class="nav-menu-user-icon">more_horiz</span>
                                    </a>
                                </div>
                                <div class="ellipsis-dropdown-content">
                                    <ul>
                                        <li @@click.once.stop.prevent="abandon(vgImport)">Abandon</li>
                                        <li @@click.once.stop.prevent="enqueue(vgImport)">Enqueue</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="import-pubmed-card-contract-text">
                            <div>
                                {{vgImport.numberOfContracts}}
                                <span v-if="vgImport.numberOfContracts == 1">Contract</span>
                                <span v-else>Contracts</span>
                            </div>
                        </div>
                        <div class="import-card-line"></div>
                        <div class="import-card-mod">
                            <div class="import-card-mod-date">
                                <div class="import-card-date-title">Mod Date From (ET)</div>
                                <div class="import-card-date">{{vgImport.dateFrom}}</div>
                            </div>
                            <div>
                                <div class="import-card-date-title">Mod Date To (ET)</div>
                                <div class="import-card-date">{{vgImport.dateTo}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else-if="vgImport.importType=='Manual'">
                    <div class="card-info" @@click.stop.prevent="viewImportDetails(vgImport)">
                        <div class="import-card-type">
                            <div class="import-type import-card-title-text adhoc">
                                {{vgImport.importType}} Import
                            </div>

                            <div class="ellipsis-container" @@click.stop>
                                <div class="ellipsis-button">
                                    <a :href="">
                                        <span class="nav-menu-user-icon">more_horiz</span>
                                    </a>
                                </div>
                                <div class="ellipsis-dropdown-content">
                                    <ul>
                                        <li @@click.once.stop.prevent="abandon(vgImport)">Abandon</li>
                                        <li @@click.once.stop.prevent="enqueue(vgImport)">Enqueue</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="import-card-journal-title">Journal Title</div>
                        </div>
                        <div class="import-card-text">{{this.truncateWithEllipses(vgImport.title, this.maxTitleLength)}}&nbsp;</div>
                        <div class="import-card-line"></div>
                        <div class="import-card-mod">
                            <div class="import-card-mod-date">
                                <div class="import-card-updatedby-title">Last updated by</div>
                                <div class="import-card-updatedby">{{vgImport.lastUpdatedBy}}</div>
                            </div>
                            <div>
                                <div class="import-card-updatedby-title">Created by</div>
                                <div class="import-card-createdby">{{vgImport.createdBy}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else-if="vgImport.importType=='File'">
                    <div class="card-info" @@click.stop.prevent="viewImportDetails(vgImport)">
                        <div class="import-card-type">

                            <div class="import-type import-card-title-text adhoc">
                                {{vgImport.importType}} Import
                            </div>

                            <div class="ellipsis-container" @@click.stop>
                                <div class="ellipsis-button">
                                    <a :href="">
                                        <span class="nav-menu-user-icon">more_horiz</span>
                                    </a>
                                </div>
                                <div class="ellipsis-dropdown-content">
                                    <ul>
                                        <li @@click.once.stop.prevent="abandon(vgImport)">Abandon</li>
                                        <li @@click.once.stop.prevent="enqueue(vgImport)">Enqueue</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="import-card-contract-text">
                                <div>
                                    {{vgImport.fileCount}}
                                    <span v-if="vgImport.fileCount == 1">File</span>
                                    <span v-else>Files</span>
                                </div>

                            </div>
                            <div class="import-card-mod">

                                <div class="import-card-file-import-lozenge" :title="vgImport.files[0]" v-if="vgImport.files[0]">{{truncateFilenameWithEllipses(vgImport.files[0], maxFileNameLength)}}</div>
                                <div class="import-card-file-import-lozenge" :title="vgImport.files[1]" v-if="vgImport.files[1]">{{truncateFilenameWithEllipses(vgImport.files[1], maxFileNameLength)}}</div>
                                <div class="import-card-file-import-lozenge" v-if="vgImport.filesPlus > 0">+{{vgImport.filesPlus}}</div>
                            </div>
                            <div class="import-card-line"></div>
                            <div class="import-card-mod">
                                <div class="import-card-mod-date">
                                    <div class="import-card-updatedby-title">Last updated by</div>
                                    <div class="import-card-updatedby">{{vgImport.lastUpdatedBy}}</div>
                                </div>
                                <div>
                                    <div class="import-card-updatedby-title">Created by</div>
                                    <div class="import-card-createdby">{{vgImport.createdBy}}</div>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>
                <div v-else-if="vgImport.importType=='Failed'">
                    <div class="card-info" @@click.stop.prevent="viewImportDetails(vgImport)">
                        <div class="import-card-type">
                            <div class="import-type import-card-title-text adhoc">
                                {{vgImport.importType}} Import
                            </div>
                            <div class="flex">
                                <div class="flex-item">
                                    <div :class="['import-card-failed-lozenge', `status-${setFailedStatusText(vgImport.failedImportStatus)}`]"> {{ setFailedStatusText(vgImport.failedImportStatus)}} <i v-if="vgImport.failedImportStatus === 20" class="m-icon info-circle" title="This failed import has been reviewed and manually corrected">info</i></div>
                                </div>
                                <div class="ellipsis-container flex-item" @@click.stop>

                                    <div class="ellipsis-button">
                                        <a :href="">
                                            <span class="nav-menu-user-icon">more_horiz</span>
                                        </a>
                                    </div>
                                    <div class="ellipsis-dropdown-content">
                                        <ul>
                                            <li @@click.once.stop.prevent="abandon(vgImport)">Abandon</li>
                                            <li @@click.once.stop.prevent="enqueue(vgImport)">Enqueue</li>
                                        </ul>
                                    </div>
                                </div>

                            </div>

                        </div>
                        <div>
                            <div class="import-card-journal-title">Failed File</div>

                            <div class="import-card-text">{{vgImport.filename}}</div>
                            <div class="import-card-line"></div>
                            <div class="import-card-mod">
                                <div class="import-card-mod-date">
                                    <div class="import-card-updatedby-title">Last updated by</div>
                                    <div class="import-card-updatedby">{{vgImport.lastUpdatedBy}}</div>
                                </div>
                                <div>
                                    <div class="import-card-updatedby-title">Created by</div>
                                    <div class="import-card-createdby">{{vgImport.createdBy}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-else-if="this.filterType == 'Failed'">
            Nothing to display, yet
        </div>

    </section>
    <manual-entry-modal :model="manualEntry" :countries="countries" :journal-titles="journalTitles" modal-id="manualEntryModal"></manual-entry-modal>
    <manual-entry-modal :model="failedImportFile" :countries="countries" :journal-titles="journalTitles" :parent-loading="dataFetching" modal-id="failedImportModal"></manual-entry-modal>

</div>



@section Scripts {

    <script type="text/javascript">

        const token = document.getElementsByName("__RequestVerificationToken")[0].value;
        var pageModel = @Html.Raw(AntiXss.ToJson(Model));
        var countries = @Html.Raw(AntiXss.ToJson(_countries));
        var journalTitles = @Html.Raw(AntiXss.ToJson(_journalTitles));
        var pageConfig = {
            appElement: "#importcards",
            data: function () {
                return {
                    types: ['All', 'Pubmed', 'File', 'Manual', 'Failed'],
                    filterType: 'All',
                    maxTitleLength: 120,
                    maxFileNameLength: 15,
                    showConfirmDialog: false,
                    failedImportFile: {},
                    countries: countries,
                    journalTitles: journalTitles,
                    manualEntry: {},
                    selectedAdHocImport: '',
                    dataFetching: false,
                    failedImportStatus: {
                        failed: 10,
                        reviewed: 20
                    },
                };
            },
            computed: {
                filteredCards() {
                    return pageModel.importDisplayCards.filter(
                        c => c.importType===this.filterType || this.filterType === 'All');
                }

            },
            methods: {
                enqueue(vgImport) {
                    if (vgImport.importType === "Pubmed") {
                        let params = { adHocImportId: vgImport.importId };
                        this.postQueueRequest(`/Import/AdHocListDetails_Enqueue/${vgImport.id}`, '/Import/ImportLog', params);
                    }
                    else {
                        this.postQueueRequest(`/Import/EnqueueImportReference`, '/Import/AdHocList', vgImport)
                    }
                },
                abandon(vgImport) {
                    if (vgImport.importType === "Pubmed") {
                        let params = { adHocImportId: vgImport.importId };
                        this.postQueueRequest(`/Import/AdHocListDetails_Abandon/${vgImport.id}`, '/Import/AdHocList', params);
                    }
                    else if (vgImport.importType === "Failed" || vgImport.importType === "Manual") {
                        this.deleteQueueRequest(`/Import/Abandon${vgImport.importType}/${vgImport.id}`, '/Import/AdHocList', vgImport.id);
                    }
                    else if (vgImport.importType === "File") {
                        let params = { adHocImportId: vgImport.importId };
                        this.postQueueRequest(`/Import/ImportFile_Abandon/${vgImport.id}`, '/Import/AdHocList', params);
                    }

                },
                postQueueRequest(url, redirectUrl, requestData) {
                    fetch(url, {
                        method: "POST",
                        body: JSON.stringify(requestData),
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    }).then(res => {
                        if (!res.ok) {
                            throw res;
                        }
                        return res;
                    }).then((data) => {
                        if (redirectUrl.length > 0) {
                            location.href = redirectUrl;
                        }
                    }).catch(error => {
                        console.log(error);
                        plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                    });
                },
                deleteQueueRequest(url, redirectUrl, id) {
                    fetch(url, {
                        method: 'DELETE',
                        credentials: 'same-origin',
                        headers: {
                            'Content-Type': 'application/json',
                            "RequestVerificationToken": token
                        }
                    }).then(res => {
                        if (res.ok) {
                            plx.toast.show('The import was abandoned successfully.', 2, 'confirm', null, 2500);
                            if (redirectUrl.length > 0) {
                                location.href = redirectUrl;
                            }
                        }
                        else {
                            return plx.toast.show('issue abandoning import.', 2, 'failed', null, 2500);
                        }
                    }).catch(error => {
                        console.log(error);
                        plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                    });

                },
                viewImportDetails(vgImport) {
                    if (vgImport.importType === "Pubmed") {
                        location.href = `/Import/AdHocListDetails/${vgImport.id}`;
                    } else if (vgImport.importType === "Manual") {
                        this.getManualEntry(vgImport.id);
                    } else if (vgImport.importType === "File") {
                        location.href = `/Import/UpdateImportFile?batchId=${vgImport.id}`;
                    } else if (vgImport.importType === "Failed") {
                        this.getFailedImportFile(vgImport.id);
                    }
                },
                truncateWithEllipses(longString, maxLength) {
                    if (!longString || longString.length === 0) {
                        return ''
                    }
                    return longString.length <= maxLength ? longString : longString.slice(0, maxLength) + "...";
                },
                truncateFilenameWithEllipses(longString, maxLength) {
                    if (!longString || longString.length === 0) {
                        return ''
                    }

                    if (longString.length <= maxLength) {
                        return longString;
                    }
                    else {
                        let fileExtension = this.getFileExtension(longString);
                        return this.truncateWithEllipses(longString, maxLength - fileExtension.length - 3) + fileExtension;
                    }
                },
                getFileExtension(filename) {
                    return filename.split('.').pop();
                },
                showModal(modalId) {
                    const modalSelector = `#${modalId}`;
                    $(modalSelector).modal('show');
                },
                handleAdHocImportChange() {
                    if (this.selectedAdHocImport === 'AdHocCreate') {
                        window.location.href = `../Import/${this.selectedAdHocImport}`;
                        this.selectedAdHocImport = '';
                    }
                    else if (this.selectedAdHocImport === 'ManualEntry') {
                        this.manualEntry = {};
                        this.showModal('manualEntryModal');
                        this.selectedAdHocImport = '';
                    }
                },
                getFailedImportFile(id) {
                    this.dataFetching = true;

                    fetch(`/Import/GetFailedImportFile/${id}`, {
                        method: "GET",
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    }).then(res => {
                        if (!res.ok) {
                            throw res;
                        }
                        return res.json();
                    }).then((failedImportFile) => {
                        this.failedImportFile = { ...failedImportFile };
                        this.$nextTick(() => {
                            this.showModal('failedImportModal');
                        });
                    }).catch(error => {
                        console.log(error);
                        plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                    }).finally(() => {
                        this.dataFetching = false;
                    });
                },
                getManualEntry(id) {
                    fetch(`/Import/GetImportReference/${id}`, {
                        method: "GET",
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    }).then(res => {
                        if (!res.ok) {
                            throw res;
                        }
                        return res.json();
                    }).then((manualEntry) => {
                        this.manualEntry = { ...manualEntry }; // Make a fresh object to trigger reactivity
                        this.$nextTick(() => {
                            this.showModal('manualEntryModal');
                        });
                    }).catch(error => {
                        console.log(error);
                        plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                    });
                },
                setFailedStatusText(failedImportStatus) {
                    if (failedImportStatus === this.failedImportStatus.failed) {
                        return 'Failed';
                    } else if (failedImportStatus === this.failedImportStatus.reviewed) {
                        return 'Reviewed';
                    }
                }
            }
        };

    </script>

}
@section VueComponentScripts {
    <partial name="Components/AuditTrailSelectionComponent" />
    <partial name="Components/ManualEntryModal" />
}

