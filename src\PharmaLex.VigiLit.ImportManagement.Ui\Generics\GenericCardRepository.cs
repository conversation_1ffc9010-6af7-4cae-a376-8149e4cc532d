﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.Core.DataAccessLayer;
using PharmaLex.DataAccess;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Generics;

internal class GenericCardRepository<TCardEntity> : TrackingGenericRepository<TCardEntity>, IGenericCardRepository<TCardEntity>
where TCardEntity: CoreEntityBase<int>
{
    public GenericCardRepository(PlxDbContext context, IUserContext userContext) : base(context, userContext.User)
    {
    }

    public async Task<IEnumerable<TCardEntity>> GetAll()
    {
        return await context.Set<TCardEntity>()
              .OrderByDescending(c => c.Id)
              .ToListAsync();
    }
    
    public async Task<TCardEntity?> GetById(int id)
    {
        return await context.Set<TCardEntity>()
            .Where(i => i.Id == id)
            .FirstOrDefaultAsync();
    }
}
