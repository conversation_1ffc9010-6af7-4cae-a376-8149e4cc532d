﻿using AutoMapper;
using Moq;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Application.Services;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using Xunit;
using PharmaLex.VigiLit.Application.AutoMapper;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;
using PharmaLex.VigiLit.Test.Fakes.Entities;

namespace PharmaLex.VigiLit.Application.Unit.Tests.Services;

public class PotentialCaseAdditionalFieldServiceTests
{
    private readonly IPotentialCaseAdditionalFieldService _potentialCaseAdditionalFieldService;
    private readonly Mock<IPotentialCaseAdditionalFieldRepository> _mockPotentialCaseAdditionalFieldRepository = new();
    private readonly Mock<IClassificationCategoryService> _mockClassificationCategoryService = new();
    
    public PotentialCaseAdditionalFieldServiceTests()
    {
        var config = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<PotentialCaseAdditionalFieldMappingProfile>();
        });
        var mapper = config.CreateMapper();

        _potentialCaseAdditionalFieldService = new PotentialCaseAdditionalFieldService(_mockPotentialCaseAdditionalFieldRepository.Object, _mockClassificationCategoryService.Object, mapper);
    }

    [Fact]
    public async Task GetAllAsync_returns_list_of_PotentialCaseAdditionalFields()
    {
        // Arrange
        _mockPotentialCaseAdditionalFieldRepository.Setup(x => x.GetAllAsync())
            .ReturnsAsync(GetPotentialCaseAdditionalFields());

        _mockClassificationCategoryService.Setup(x => x.GetByIdAsync(1))
            .ReturnsAsync(GetPotentialCaseCategory());

        // Act
        var result = await _potentialCaseAdditionalFieldService.GetAllAsync();

        // Assert
        Assert.Equal(5, result.Count());
    }

    [Fact]
    public async Task SetPotentialCaseAdditionalInformation_sets_list_of_potential_case_additional_information_for_bracketed_list()
    {
        // Arrange
        _mockPotentialCaseAdditionalFieldRepository.Setup(x => x.GetAllAsync())
            .ReturnsAsync(GetPotentialCaseAdditionalFields());

        _mockClassificationCategoryService.Setup(x => x.GetByIdAsync(1))
            .ReturnsAsync(GetPotentialCaseCategory());

        var printPreviewPageModel = new PrintPreviewPageModel
        {
            Classifications = new List<PrintPreviewSearchResultModel>
            {
                new() {ClassificationCategory = "Potential Case", PotentialCaseAdditionalInformation = "[1][2][3]"}
            }
        };

        // Act
        await _potentialCaseAdditionalFieldService.SetPotentialCaseAdditionalInformation(printPreviewPageModel);

        // Assert
        Assert.Equal(3, printPreviewPageModel.Classifications.First().PotentialCaseAdditionalInformationList.Count);

        Assert.Equal("Field 1", printPreviewPageModel.Classifications.First().PotentialCaseAdditionalInformationList[0]);
        Assert.Equal("Field 2", printPreviewPageModel.Classifications.First().PotentialCaseAdditionalInformationList[1]);
        Assert.Equal("Field 3", printPreviewPageModel.Classifications.First().PotentialCaseAdditionalInformationList[2]);
    }

    [Fact]
    public async Task SetPotentialCaseAdditionalInformation_sets_empty_list_of_potential_case_additional_information_for_non_potential_case()
    {
        // Arrange
        _mockPotentialCaseAdditionalFieldRepository.Setup(x => x.GetAllAsync())
            .ReturnsAsync(GetPotentialCaseAdditionalFields());

        _mockClassificationCategoryService.Setup(x => x.GetByIdAsync(1))
            .ReturnsAsync(GetPotentialCaseCategory());

        var printPreviewPageModel = new PrintPreviewPageModel
        {
            Classifications = new List<PrintPreviewSearchResultModel>
            {
                new() {ClassificationCategory = "Safety relevant information", PotentialCaseAdditionalInformation = null}
            }
        };

        // Act
        await _potentialCaseAdditionalFieldService.SetPotentialCaseAdditionalInformation(printPreviewPageModel);

        // Assert
        Assert.Empty(printPreviewPageModel.Classifications.First().PotentialCaseAdditionalInformationList);
    }

    [Fact]
    public async Task SetPotentialCaseAdditionalInformation_sets_empty_list_of_potential_case_additional_information_for_empty_list()
    {
        // Arrange
        _mockPotentialCaseAdditionalFieldRepository.Setup(x => x.GetAllAsync())
            .ReturnsAsync(GetPotentialCaseAdditionalFields());

        _mockClassificationCategoryService.Setup(x => x.GetByIdAsync(1))
            .ReturnsAsync(GetPotentialCaseCategory());

        var printPreviewPageModel = new PrintPreviewPageModel
        {
            Classifications = new List<PrintPreviewSearchResultModel>
            {
                new() {ClassificationCategory = "Potential Case", PotentialCaseAdditionalInformation = null}
            }
        };

        // Act
        await _potentialCaseAdditionalFieldService.SetPotentialCaseAdditionalInformation(printPreviewPageModel);

        // Assert
        Assert.Empty(printPreviewPageModel.Classifications.First().PotentialCaseAdditionalInformationList);
    }

    private List<PotentialCaseAdditionalField> GetPotentialCaseAdditionalFields()
    {
        return
        [
            new FakePotentialCaseAdditionalField(1) { Name = "Field 1" },
            new FakePotentialCaseAdditionalField(2) { Name = "Field 2" },
            new FakePotentialCaseAdditionalField(3) { Name = "Field 3" },
            new FakePotentialCaseAdditionalField(4) { Name = "Field 4" },
            new FakePotentialCaseAdditionalField(5) { Name = "Field 5" }
        ];
    }

    private static ClassificationCategoryModel GetPotentialCaseCategory()
    {
        return new ClassificationCategoryModel { Name = "Potential Case" };
    }

}