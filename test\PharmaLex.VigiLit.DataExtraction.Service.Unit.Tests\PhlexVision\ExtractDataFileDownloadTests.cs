﻿using Microsoft.Extensions.Options;
using Xunit;
using Moq;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Import;
using PharmaLex.VigiLit.DataExtraction.Service.Extensions;
using PharmaLex.VigiLit.Infrastructure.Utilities;

namespace PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests.PhlexVision;

public class ExtractDataFileDownloadTests
{
    private readonly IExtractDataFileDownload _extractDataFileDownload;

    private readonly Mock<IDocumentService> _mockDocumentService = new();
    private readonly IOptions<AzureStorageImportFileDocumentUploadOptions> _azureStorageImportFileDocumentUploadOptions = Options.Create(new AzureStorageImportFileDocumentUploadOptions() { AccountName = "Account", ContainerName = "UploadContainer" });
    private readonly IOptions<AzureStorageImportFileDocumentOptions> _azureStorageImportFileDocumentOptions = Options.Create(new AzureStorageImportFileDocumentOptions() { AccountName = "Account", ContainerName = "FileContainer" });

    public ExtractDataFileDownloadTests()
    {
        _extractDataFileDownload = new ExtractDataFileDownload(_mockDocumentService.Object, _azureStorageImportFileDocumentUploadOptions, _azureStorageImportFileDocumentOptions);
    }

    [Fact]
    public async Task Given_CreateTextBlob_is_called_then_a_blob_is_created_with_correct_path_and_data()
    {
        // Arrange
        var text = "Raw text";
        var batchId = Guid.NewGuid();
        var fileName = "Filename.pdf";
        var stream = text.ToStream();
        var streamHelper = new StreamHelper();

        _mockDocumentService.Setup(x => x.Create(
                                                It.IsAny<DocumentDescriptor>(),
                                                                It.IsAny<Stream>(),
                                                 It.IsAny<CancellationToken>()));

        // Act
        await _extractDataFileDownload.CreateTextBlob(batchId, fileName, text);

        // Assert
        _mockDocumentService.Verify(x => x.Create(
            It.Is<DocumentDescriptor>(d => d.BlobName == $"{batchId}/{fileName}"),
                            It.Is<Stream>(s => streamHelper.AreStreamsIdentical(s, (MemoryStream)stream)),
             It.IsAny<CancellationToken>()
            ));
    }

    [Fact]
    public async Task Given_CreateTextBlobWithStorageMetaData_is_called_then_a_blob_is_created_with_correct_metadata()
    {
        // Arrange
        var text = "Raw text";
        var batchId = Guid.NewGuid();
        var fileName = "Filename.pdf";
        var stream = text.ToStream();
        var streamHelper = new StreamHelper();

        _mockDocumentService.Setup(x => x.Create(
                                                It.IsAny<DocumentDescriptor>(),
                                                                It.IsAny<Stream>(),
                                                 It.IsAny<CancellationToken>()));

        // Act
        var blobStoragePath = await _extractDataFileDownload.CreateTextBlobWithStorageMetaData(batchId, fileName, text);

        // Assert
        _mockDocumentService.Verify(x => x.Create(
            It.Is<DocumentDescriptor>(d => d.BlobName == $"{batchId}/{fileName}"),
                            It.Is<Stream>(s => streamHelper.AreStreamsIdentical(s, (MemoryStream)stream)),
             It.IsAny<CancellationToken>()
            ));
    }
}