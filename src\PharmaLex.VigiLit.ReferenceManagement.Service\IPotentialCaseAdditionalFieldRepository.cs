﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using Microsoft.EntityFrameworkCore;

namespace PharmaLex.VigiLit.ReferenceManagement.Service
{
    internal class PotentialCaseAdditionalFieldRepository : TrackingGenericRepository<PotentialCaseAdditionalField>, IPotentialCaseAdditionalFieldRepository
    {
        public PotentialCaseAdditionalFieldRepository(PlxDbContext context, IUserContext userContext)
            : base(context, userContext.User)
        {
        }

        public async Task<IEnumerable<PotentialCaseAdditionalField>> GetAllAsync()
        {
            return await context.Set<PotentialCaseAdditionalField>()
                .OrderBy(x => x.Name)
                .AsNoTracking()
                .ToListAsync();
        }
    }
}
