﻿using PharmaLex.Core.UserManagement;
using PharmaLex.Core.UserManagement.Claims;
using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Models;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using StaticClaims = PharmaLex.VigiLit.Domain.UserManagement.Claims;

namespace PharmaLex.VigiLit.Domain.UserManagement;

public class User : VigiLitEntityBase, IUserEntity
{
    public string Email { get;  set; }
    public string GivenName { get;  set; }
    public string FamilyName { get;  set; }
    public DateTime? LastLoginDate { get; protected set; }
    public int QCPercentage { get; set; }

    public DateTime? ActivationExpiryDate { get; set; }
    public string InvitationEmailLink { get; set; }

    protected internal ICollection<Claim> ClaimsInternal { get; set; }
    public IReadOnlyCollection<IClaimEntity> GetClaims() => new ReadOnlyCollection<Claim>(ClaimsInternal.ToList());

    public ICollection<UserSubstance> UserSubstances { get; set; }

    public CompanyUser CompanyUser { get; set; }

    public EmailSuppression EmailSuppression { get; set; }

    public ICollection<UserEmailPreference> UserEmailPreferences { get; set; }
    public ICollection<ReferenceClassificationLock> ReferenceClassificationLocks { get; set; }

    public ICollection<ContractVersion> ContractVersions { get; set; }
    public User()
    {
        ClaimsInternal = new HashSet<Claim>();
        UserEmailPreferences = new HashSet<UserEmailPreference>();
    }

    public User(string givenName, string familyName, string email) : this()
    {
        UpdateUserData(givenName, familyName, email);
    }

    public void UpdateUserData(string givenName, string familyName, string email)
    {
        if (string.IsNullOrEmpty(givenName))
        {
            throw new ArgumentNullException($"{givenName} is required");
        }

        if (string.IsNullOrEmpty(familyName))
        {
            throw new ArgumentNullException($"{familyName} is required");
        }

        if (string.IsNullOrEmpty(email))
        {
            throw new ArgumentNullException($"{email} is required");
        }

        GivenName = givenName;
        FamilyName = familyName;
        Email = email;
    }

    public void UpdateLastLoginDate()
    {
        LastLoginDate = DateTime.UtcNow;
    }

    public void AddClaim(Claim claim)
    {
        if (claim == default)
        {
            throw new ArgumentNullException($"{claim} not provided");
        }

        ClaimsInternal.Add(claim);
    }

    public void AddClaims(IEnumerable<Claim> claims)
    {
        foreach (var claim in claims)
        {
            AddClaim(claim);
        }
    }

    public void RemoveClaim(int claimId)
    {
        if (claimId == default)
        {
            throw new ArgumentNullException($"{claimId} not provided");
        }

        var userClaim = ClaimsInternal.FirstOrDefault(u => u.Id == claimId);

        if (userClaim == null)
        {
            throw new ArgumentException("Claim not found in user claims");
        }

        ClaimsInternal.Remove(userClaim);
    }

    public bool IsCompanyUser()
    {
        return this.GetClaims().Any(c => c.Name == StaticClaims.ClientResearcher);
    }

    public bool HasActiveCompany()
    {
        return this.GetActiveCompanyId() > 0;
    }

    public int GetActiveCompanyId()
    {
        if (this.CompanyUser.Active)
        {
            return this.CompanyUser.CompanyId;
        }

        return -1;
    }
}
