﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class FixSourceSystemForPubmedReferences : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("exec('update ReferenceUpdates set SourceSystem = 1 where SourceSystem = 0 and ISNUMERIC(SourceId) = 1 and CreatedBy = ''<EMAIL>''')");
            migrationBuilder.Sql("exec('update [References] set SourceSystem = 1 where SourceSystem = 0 and ISNUMERIC(SourceId) = 1 and CreatedBy = ''<EMAIL>''')");

            migrationBuilder.Sql("exec('SET TRANSACTION ISOLATION LEVEL SERIALIZABLE; ALTER TABLE [References] SET (SYSTEM_VERSIONING = OFF)')");
            migrationBuilder.Sql("exec('update [ReferencesHistory] set SourceSystem = 1 where SourceSystem = 0 and ISNUMERIC(SourceId) = 1 and CreatedBy = ''<EMAIL>''; ALTER TABLE [References] SET (SYSTEM_VERSIONING = ON (  HISTORY_TABLE = dbo.ReferencesHistory, DATA_CONSISTENCY_CHECK = ON))')");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
