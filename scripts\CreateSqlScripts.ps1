
$Build_SourcesDirectory = ".."
$Build_artifactstagingdirectory = "C:\TEMP"
$BuildConfiguration = "Release"


dotnet.exe ef migrations script --project "$Build_SourcesDirectory\src\PharmaLex.VigiLit.Infrastructure.Data\PharmaLex.VigiLit.Infrastructure.Data.csproj" --startup-project "$Build_SourcesDirectory\src\PharmaLex.VigiLit.Web\PharmaLex.VigiLit.Web.csproj" --output $Build_artifactstagingdirectory\Migrations\migration.sql --idempotent --verbose --configuration $BuildConfiguration