﻿<script type="text/x-template" id="scheduler-settings-panel">
    <div class="scheduler-settings-container" @@click.stop>
        <div class="schedule-section">
            <h3 class="section-title">Schedule</h3>

            <div class="repeat-controls">
                <label class="repeat-label">Repeat every</label>
                <div class="repeat-input-group">
                    <input type="number"
                           v-model="repeatInterval"
                           min="1"
                           max="4"
                           class="repeat-number-input"
                           @@input="updateSchedule"/>

                    <select v-model="repeatUnit"
                            class="repeat-unit-select"
                            @@change="updateSchedule">
                        <option value="week">Week</option>
                        <option value="month">Month</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">
    vueApp.component('scheduler-settings', {
        template: '#scheduler-settings-panel',
        emits: ['update:modelValue'],
        props: {
            defaultInterval: {
                type: Number,
                default: 1
            },
            defaultUnit: {
                type: String,
                default: 'week'
            },
            modelValue: {
                type: String,
                default: ''
            }
        },
        data() {
            return {
                repeatInterval: 1,
                repeatUnit: 'week'
            }
        },
        methods: {
            updateSchedule() {
                const cronExpression = this.generateCronExpression();
                this.$emit('update:modelValue', cronExpression);
            },
            generateCronExpression() {
                // Cron format: second minute hour day month dayOfWeek
                const interval = parseInt(this.repeatInterval);
                switch (this.repeatUnit) {
                    case 'week':
                        if (interval === 1) {
                            return '0 0 0 * * 0'; // Every Sunday at midnight (weekly)
                        } else {
                            // For bi-weekly (every 2 weeks), tri-weekly (every 3 weeks), etc.
                            // Note: Standard cron doesn't support "every N weeks" directly
                            // This creates a custom format that your scheduler needs to interpret
                            return `0 0 0 * * 0/${interval}`; // Custom: every N weeks on Sunday
                        }
                    case 'month':
                        if (interval === 1) {
                            return '0 0 0 1 * *'; // Every month on the 1st (monthly)
                        } else {
                            // For bi-monthly (every 2 months), quarterly (every 3 months), etc.
                            return `0 0 0 1 */${interval} *`; // Every N months on the 1st
                        }
                    default:
                        return '0 0 0 * * *'; // Default to daily
                }
            },
            parseCronExpression(cronExp) {
                if (!cronExp) return;

                const parts = cronExp.split(' ');
                if (parts.length < 6) return;

                // Check for custom weekly interval notation (dayOfWeek field with /)
                if (parts[5].includes('/')) {
                    const dayOfWeekPart = parts[5].split('/');
                    if (dayOfWeekPart[0] === '0') { // Sunday
                        this.repeatInterval = parseInt(dayOfWeekPart[1]) || 1;
                        this.repeatUnit = 'week';
                        return;
                    }
                }

                // Parse monthly patterns with interval
                if (parts[4].includes('/')) {
                    const monthInterval = parts[4].split('/')[1];
                    this.repeatInterval = parseInt(monthInterval) || 1;
                    this.repeatUnit = 'month';
                }
                // Simple weekly (every Sunday = once weekly)
                else if (parts[5] === '0' && parts[3] === '*') {
                    this.repeatInterval = 1;
                    this.repeatUnit = 'week';
                }
                // Simple monthly (every 1st = once monthly)
                else if (parts[3] === '1' && parts[4] === '*') {
                    this.repeatInterval = 1;
                    this.repeatUnit = 'month';
                }
            }
        },
        watch: {
            modelValue: {
                handler(newValue) {
                    if (newValue) {
                        this.parseCronExpression(newValue);
                    }
                },
                immediate: true
            }
        },
        mounted() {
            if (this.modelValue) {
                this.parseCronExpression(this.modelValue);
            } else {
                this.repeatInterval = this.defaultInterval;
                this.repeatUnit = this.defaultUnit;
            }
            this.updateSchedule();
        }
    });
</script>

