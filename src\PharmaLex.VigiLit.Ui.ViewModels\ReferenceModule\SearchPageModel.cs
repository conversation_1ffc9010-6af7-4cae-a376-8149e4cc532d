﻿using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using System.Collections.Generic;

namespace PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

public class SearchPageModel
{
    public IEnumerable<SubstanceItemModel> Substances { get; set; }

    public IEnumerable<ClassificationCategoryModel> ClassificationCategories { get; set; }

    public IEnumerable<CompanyItemModel> Companies { get; set; }

    public bool DisplayLastUpdatedDateFilter { get; set; }

    public IEnumerable<PotentialCaseAdditionalFieldModel> SpecialSituations { get; set; }
}