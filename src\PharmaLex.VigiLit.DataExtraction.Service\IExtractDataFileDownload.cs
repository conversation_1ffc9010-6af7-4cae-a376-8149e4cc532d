﻿using PharmaLex.BlobStorage.Descriptors;

namespace PharmaLex.VigiLit.DataExtraction.Service;

internal interface IExtractDataFileDownload
{
    Task<DownloadFile> DownloadImportedFile(Guid batchId, string fileName);
    Task Delete<T>(T fileDescriptor, CancellationToken cancellationToken = default);
    Task<bool> Exists<T>(T fileDescriptor, CancellationToken cancellationToken = default);
    Task<DocumentProperties> Copy(ImportFileUploadDescriptor importFileUploadDescriptor, ImportFileDescriptor importFileDescriptor, CancellationToken cancellationToken = default);
    Task<DocumentProperties> CopyBack(
            ImportFileDescriptor importFileDescriptor,
            ImportFileUploadDescriptor importFileUploadDescriptor,
            CancellationToken cancellationToken = default);

    /// <summary>
    /// Saves text to blob storage
    /// </summary>
    /// <param name="batchId">Batch id for constructing path name</param>
    /// <param name="fileName">File name for saving resultant blob</param>
    /// <param name="text">Text to save</param>
    Task CreateTextBlob(Guid batchId, string fileName, string text);

    /// <summary>
    /// Saves text to blob storage 
    /// </summary>
    /// <param name="batchId">Batch id for constructing path name</param>
    /// <param name="fileName">File name for saving resultant blob</param>
    /// <param name="text">Text to save</param>
    /// <returns>Object containing blob metadata</returns>
    Task<BlobStoragePath> CreateTextBlobWithStorageMetaData(Guid batchId, string fileName, string text);
}