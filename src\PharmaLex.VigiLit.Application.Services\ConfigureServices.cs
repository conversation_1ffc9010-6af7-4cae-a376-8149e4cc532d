﻿using Microsoft.Extensions.DependencyInjection;
using PharmaLex.VigiLit.Application.Services.Helpers;
using PharmaLex.VigiLit.Domain.Interfaces.Services;

namespace PharmaLex.VigiLit.Application.Services;

public static class ConfigureServices
{
    public static void RegisterApplicationServices(this IServiceCollection services)
    {
        services.AddScoped<IPreClassifyModelHelper, PreClassifyModelHelper>();
        services.AddScoped<IPotentialCaseAdditionalFieldService, PotentialCaseAdditionalFieldService>();
    }
}
