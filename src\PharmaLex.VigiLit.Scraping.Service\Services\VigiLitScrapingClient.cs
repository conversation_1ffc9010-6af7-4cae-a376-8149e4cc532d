using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.ContractManagement.Ui.Journals;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class VigiLitScrapingClient : IVigiLitScrapingClient
{
    private readonly IApifyClient _apifyClient;
    private readonly IJournalRepository _journalRepository;
    private readonly ILogger<VigiLitScrapingClient> _logger;
    private readonly IConfiguration _configuration;

    public VigiLitScrapingClient(
        IApifyClient apifyClient,
        IJournalRepository journalRepository,
        ILogger<VigiLitScrapingClient> logger,
        IConfiguration configuration)
    {
        _apifyClient = apifyClient;
        _journalRepository = journalRepository;
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<RestoreSchedulesResult> RestoreSchedulesAsync(CancellationToken cancellationToken = default)
    {
        var result = new RestoreSchedulesResult();
        
        try
        {
            _logger.LogInformation("Starting restoration of Apify schedules for existing journals");

            // Get all enabled journals with schedules
            var journals = await _journalRepository.GetAll();
            var enabledJournalsWithSchedules = journals
                .Where(j => j.Enabled && !string.IsNullOrEmpty(j.CronExpression))
                .ToList();

            result.JournalsProcessed = enabledJournalsWithSchedules.Count;
            _logger.LogInformation("Found {Count} enabled journals with schedules", result.JournalsProcessed);

            if (!enabledJournalsWithSchedules.Any())
            {
                result.Messages.Add("No enabled journals with schedules found");
                return result;
            }

            // Group journals by CronExpression (schedule)
            var journalGroups = enabledJournalsWithSchedules
                .GroupBy(j => j.CronExpression)
                .ToList();

            _logger.LogInformation("Grouped journals into {Count} schedule groups", journalGroups.Count());

            // Get webhook URL from configuration
            var webhookUrl = GetWebhookUrl();
            if (string.IsNullOrEmpty(webhookUrl))
            {
                result.Errors.Add("Webhook URL not configured");
                return result;
            }

            foreach (var group in journalGroups)
            {
                try
                {
                    await ProcessScheduleGroup(group, webhookUrl, result, cancellationToken);
                }
                catch (Exception ex)
                {
                    var errorMessage = $"Error processing schedule group with cron '{group.Key}': {ex.Message}";
                    _logger.LogError(ex, errorMessage);
                    result.Errors.Add(errorMessage);
                }
            }

            _logger.LogInformation("Completed restoration. Schedules: {Schedules}, Tasks: {Tasks}, Webhooks: {Webhooks}, Errors: {Errors}",
                result.SchedulesCreated, result.TasksCreated, result.WebhooksCreated, result.Errors.Count);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during schedule restoration");
            result.Errors.Add($"General error: {ex.Message}");
            return result;
        }
    }

    private async Task ProcessScheduleGroup(
        IGrouping<string, Domain.Models.Journal> group, 
        string webhookUrl, 
        RestoreSchedulesResult result, 
        CancellationToken cancellationToken)
    {
        var cronExpression = group.Key;
        var journals = group.ToList();
        
        _logger.LogInformation("Processing schedule group with cron '{Cron}' containing {Count} journals", 
            cronExpression, journals.Count);

        // Create a task for each journal in this schedule group
        foreach (var journal in journals)
        {
            try
            {
                await ProcessJournal(journal, cronExpression, webhookUrl, result, cancellationToken);
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error processing journal '{journal.Name}': {ex.Message}";
                _logger.LogError(ex, errorMessage);
                result.Errors.Add(errorMessage);
            }
        }
    }

    private async Task ProcessJournal(
        Domain.Models.Journal journal, 
        string cronExpression, 
        string webhookUrl, 
        RestoreSchedulesResult result, 
        CancellationToken cancellationToken)
    {
        var taskName = $"VigiLit_{journal.Name}_{journal.Id}";
        var scheduleName = $"Schedule_{journal.Name}_{journal.Id}";

        _logger.LogInformation("Processing journal '{Name}' (ID: {Id})", journal.Name, journal.Id);

        // Create task for this journal
        var taskId = await CreateTaskForJournal(journal, taskName, cancellationToken);
        if (!string.IsNullOrEmpty(taskId))
        {
            result.TasksCreated++;
            result.Messages.Add($"Created task '{taskName}' for journal '{journal.Name}'");

            // Create schedule for this task
            var scheduleId = await CreateScheduleForTask(taskId, scheduleName, cronExpression, cancellationToken);
            if (!string.IsNullOrEmpty(scheduleId))
            {
                result.SchedulesCreated++;
                result.Messages.Add($"Created schedule '{scheduleName}' for journal '{journal.Name}'");
            }

            // Create webhook for this task
            var webhookId = await CreateWebhookForTask(taskId, webhookUrl, cancellationToken);
            if (!string.IsNullOrEmpty(webhookId))
            {
                result.WebhooksCreated++;
                result.Messages.Add($"Created webhook for task '{taskName}'");
            }
        }
    }

    private async Task<string> CreateTaskForJournal(Domain.Models.Journal journal, string taskName, CancellationToken cancellationToken)
    {
        try
        {
            var urls = new List<string> { journal.Url };
            var taskId = await _apifyClient.CreateTaskAsync(taskName, urls, 1, cancellationToken);
            
            _logger.LogInformation("Created Apify task '{TaskName}' with ID '{TaskId}' for journal '{JournalName}'", 
                taskName, taskId, journal.Name);
            
            return taskId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create task '{TaskName}' for journal '{JournalName}'", taskName, journal.Name);
            throw;
        }
    }

    private async Task<string> CreateScheduleForTask(string taskId, string scheduleName, string cronExpression, CancellationToken cancellationToken)
    {
        try
        {
            var timeZone = "UTC"; // Default timezone
            var scheduleResponse = await _apifyClient.CreateSchedulesAsync(taskId, scheduleName, cronExpression, timeZone, cancellationToken);
            
            _logger.LogInformation("Created Apify schedule '{ScheduleName}' with ID '{ScheduleId}' for task '{TaskId}'", 
                scheduleName, scheduleResponse.Id, taskId);
            
            return scheduleResponse.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create schedule '{ScheduleName}' for task '{TaskId}'", scheduleName, taskId);
            throw;
        }
    }

    private async Task<string> CreateWebhookForTask(string taskId, string webhookUrl, CancellationToken cancellationToken)
    {
        try
        {
            var webhookResponse = await _apifyClient.CreateWebhookAsync(webhookUrl, taskId, cancellationToken);
            
            _logger.LogInformation("Created Apify webhook with ID '{WebhookId}' for task '{TaskId}'", 
                webhookResponse.Id, taskId);
            
            return webhookResponse.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create webhook for task '{TaskId}'", taskId);
            throw;
        }
    }

    private string GetWebhookUrl()
    {
        // Get the webhook URL from configuration
        // This should be the URL where Apify will send webhook notifications
        var baseUrl = _configuration["HostUri"] ?? _configuration["WebsiteUri"];
        if (string.IsNullOrEmpty(baseUrl))
        {
            _logger.LogWarning("No base URL configured for webhooks");
            return string.Empty;
        }

        // Construct the webhook endpoint URL
        var webhookPath = "/api/apify/webhook"; // This should match the webhook controller endpoint
        return $"{baseUrl.TrimEnd('/')}{webhookPath}";
    }
}
