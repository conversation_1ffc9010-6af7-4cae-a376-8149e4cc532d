﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Service.QualityControl;
using Xunit;

namespace PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests;
public class JournalTitleMatchingCheckerTests
{
    private readonly JournalTitleChecker _journalTitleMatchingChecker;
    private readonly Mock<ILogger<JournalTitleChecker>> _mockLogger = new();
    private readonly Mock<IJournalRepository> _journalRepository= new();

    public JournalTitleMatchingCheckerTests()
    {
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?> { { "DataExtraction:FuzzySharpJournalMatchThreshold", "90" }, })
            .Build();
        _journalRepository.Setup(x => x.GetNames()).ReturnsAsync(JournalNames());
        _journalTitleMatchingChecker = new JournalTitleChecker(_mockLogger.Object, _journalRepository.Object, configuration);
    }

    [Fact]    
    public void JournalTitleMatcher_MatchFound_ReturnsTrue()
    {
        //Arrange
        var extractedReference = new ExtractedReference
        {
            Title = null!,
            Abstract = null!,
            Authors = [],
            Affiliations = [],
            Doi = null!,
            IssueNumber = null!,
            Volume = null!,
            Issn = null!,
            Year = null!,
            Pages = null!,
            CountryOfOccurrence = null!,
            Keywords = null!,
            JournalTitle = new JournalTitle() { Confidence = 1, Value = "Mabl Journal Edited" }
        };

        // Act
        var result = _journalTitleMatchingChecker.IsValid(extractedReference);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public void JournalTitleMatcher_NotMatchFound_ReturnsFalse()
    {
        //Arrange
        var extractedReference = new ExtractedReference
        {
            Title = null!,
            Abstract = null!,
            Authors = [],
            Affiliations = [],
            Doi = null!,
            IssueNumber = null!,
            Volume = null!,
            Issn = null!,
            Year = null!,
            Pages = null!,
            CountryOfOccurrence = null!,
            Keywords = null!,
            JournalTitle = new JournalTitle() { Confidence = 1, Value = "Journal1 Edited" }
        };
        // Act
        var result = _journalTitleMatchingChecker.IsValid(extractedReference);

        // Assert
        Assert.False(result);
    }

    private static List<string> JournalNames()
    {
        var journalNames = new List<string>
        {
            "Mabl Journal Edited",
            "Acta Obstétrica e Ginecológica Portuguesa",
            "Acta Portuguesa de Nutrição",
            "Acta Radiológica Portuguesa",
            "Acta Urológica Portuguesa",
            "AIMGF Magazine",
            "Alertas de Segurança (INFARMED)",
            "Angiologia e Cirurgia Vascular",
            "Biomedical and Biopharmaceutical Research",
            "Boletim de Farmacovigilância (INFARMED)",
            "Congresso de Medicina Interna",
            "Medicina Interna"
        };
        return journalNames;
    }
}