﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32112.339
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Web", "src\PharmaLex.VigiLit.Web\PharmaLex.VigiLit.Web.csproj", "{F48742C9-5762-4F8C-A6FE-DBB786D20BC1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Application", "src\PharmaLex.VigiLit.Application\PharmaLex.VigiLit.Application.csproj", "{13162086-EAA0-4F04-9362-0CD840991171}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Domain", "src\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj", "{DD7EFF21-975F-45BD-86E5-4E8BEA64C8A7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Infrastructure.Data", "src\PharmaLex.VigiLit.Infrastructure.Data\PharmaLex.VigiLit.Infrastructure.Data.csproj", "{D6B94273-1C9D-4339-A4C7-7C3E5ECCBEC9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "config", "config", "{E72FF5D7-F294-493D-85FA-88AD5BEB120E}"
	ProjectSection(SolutionItems) = preProject
		.codegenconfig = .codegenconfig
		analyse.yml = analyse.yml
		azure-pipelines-ai.yml = azure-pipelines-ai.yml
		azure-pipelines-checkmarx.yml = azure-pipelines-checkmarx.yml
		azure-pipelines-deploy-dr.yaml = azure-pipelines-deploy-dr.yaml
		azure-pipelines-deploy.yaml = azure-pipelines-deploy.yaml
		azure-pipelines-sonar.yml = azure-pipelines-sonar.yml
		azure-pipelines.yml = azure-pipelines.yml
		build-test-analyse.yml = build-test-analyse.yml
		docker-compose.CI.yml = docker-compose.CI.yml
		docker-compose.yml = docker-compose.yml
		Dockerfile = Dockerfile
		Project Dependency Diagram.dgml = Project Dependency Diagram.dgml
		README-AI-FEATURE.md = README-AI-FEATURE.md
		README-REPORTING.md = README-REPORTING.md
		README.md = README.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "templates", "templates", "{C0463953-9BFB-4410-9DDD-EBB7F0EBA8E1}"
	ProjectSection(SolutionItems) = preProject
		codegen\Controller.cs = codegen\Controller.cs
		codegen\Edit.cshtml = codegen\Edit.cshtml
		codegen\Entity.cs = codegen\Entity.cs
		codegen\EntityConfiguration.cs = codegen\EntityConfiguration.cs
		codegen\History.cshtml = codegen\History.cshtml
		codegen\HistoryModel.cs = codegen\HistoryModel.cs
		codegen\Index.cshtml = codegen\Index.cshtml
		codegen\IRepository.cs = codegen\IRepository.cs
		codegen\IService.cs = codegen\IService.cs
		codegen\MappingProfile.cs = codegen\MappingProfile.cs
		codegen\Model.cs = codegen\Model.cs
		codegen\Repository.cs = codegen\Repository.cs
		codegen\Service.cs = codegen\Service.cs
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Infrastructure.Import.PubMed", "src\PharmaLex.VigiLit.Infrastructure.Import.PubMed\PharmaLex.VigiLit.Infrastructure.Import.PubMed.csproj", "{898D4FA7-7FEB-4DCC-A832-8B6EB71A59CB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Infrastructure", "src\PharmaLex.VigiLit.Infrastructure\PharmaLex.VigiLit.Infrastructure.csproj", "{37662230-5F49-417D-8DDA-16201E9782DE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ImportApp", "src\PharmaLex.VigiLit.ImportApp\PharmaLex.VigiLit.ImportApp.csproj", "{EB982B83-C16C-4598-B3F1-77D0D406CF00}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{F256B9A9-A9D9-4816-8BC1-56DE888AC25A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests", "test\PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests\PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests.csproj", "{198627AE-7997-44C9-9F1B-2F1E5DC344EC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore", "test\PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore\PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore.csproj", "{006D24E9-8BEA-4D62-9657-0EAFA878E800}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Test.Framework", "test\PharmaLex.VigiLit.Test.Framework\PharmaLex.VigiLit.Test.Framework.csproj", "{8EB85008-C351-45FD-B3F7-2A1A0178ECE7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{81A75B69-6E43-43F6-93D6-2BB2D25F7659}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Reporting.Contracts", "src\PharmaLex.VigiLit.Reporting.Contracts\PharmaLex.VigiLit.Reporting.Contracts.csproj", "{44D805D2-CF1A-4A54-A098-8D5D81DF6B21}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Domain.Interfaces", "src\PharmaLex.VigiLit.Domain.Interfaces\PharmaLex.VigiLit.Domain.Interfaces.csproj", "{79A3F0F3-DE7E-4C80-BB3E-7700DD17E898}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Reporting", "src\PharmaLex.VigiLit.Reporting\PharmaLex.VigiLit.Reporting.csproj", "{0DF41E4F-7719-486D-8DB6-3347BC274C44}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Data.Repositories", "src\PharmaLex.VigiLit.Data.Repositories\PharmaLex.VigiLit.Data.Repositories.csproj", "{4AC7E4EB-859C-4BFA-B7E1-FF5E67B83F49}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Application.Services", "src\PharmaLex.VigiLit.Application.Services\PharmaLex.VigiLit.Application.Services.csproj", "{6BA48300-BF93-4C7F-9B79-C839BAB2BF59}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Test.Fakes", "test\PharmaLex.VigiLit.Test.Fakes\PharmaLex.VigiLit.Test.Fakes.csproj", "{316F2B4F-B6C6-4947-9859-A4746CC4A647}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.AccessControl", "src\PharmaLex.VigiLit.AccessControl\PharmaLex.VigiLit.AccessControl.csproj", "{86BDC0E6-0F86-4867-8E7C-37AA09C6FB02}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Case", "src\PharmaLex.VigiLit.Case\PharmaLex.VigiLit.Case.csproj", "{7D6B217D-34E3-40F6-A9E6-E9270143789D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.MessageBroker.Contracts", "src\PharmaLex.VigiLit.MessageBroker.Contracts\PharmaLex.VigiLit.MessageBroker.Contracts.csproj", "{FB69913C-960D-40AF-84E9-AC90C6529033}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.AiAnalysis.Service", "src\PharmaLex.VigiLit.AiAnalysis.Service\PharmaLex.VigiLit.AiAnalysis.Service.csproj", "{9CBC2B88-2AEE-433B-A1A5-D30B7EC05442}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.AiAnalysis.Integration.Tests", "test\PharmaLex.VigiLit.AiAnalysis.Integration.Tests\PharmaLex.VigiLit.AiAnalysis.Integration.Tests.csproj", "{FFB530E3-C877-4D8C-9D78-CB51FB585D91}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.AiAnalysis.Entities", "src\PharmaLex.VigiLit.AiAnalysis.Entities\PharmaLex.VigiLit.AiAnalysis.Entities.csproj", "{5253F2F4-71D4-47F0-AB9D-1C4AE8740EA1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.AiAnalysis.Client", "src\PharmaLex.VigiLit.AiAnalysis.Client\PharmaLex.VigiLit.AiAnalysis.Client.csproj", "{BEAA2497-372B-4DC7-8AAC-75936FB1CB01}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{9435A306-32D5-45CF-BBDC-5E0756909BA7}"
	ProjectSection(SolutionItems) = preProject
		docs\ai-architecture-overview.png = docs\ai-architecture-overview.png
		docs\ai-software-workflow.png = docs\ai-software-workflow.png
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.DataAccessLayer", "src\PharmaLex.VigiLit.DataAccessLayer\PharmaLex.VigiLit.DataAccessLayer.csproj", "{22ACC837-6B65-4E53-8CD7-3C346E22BC1A}"
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{F9117B6C-1E26-4637-963E-0DCDB39FB544}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.Core.HealthCheck", "src\PharmaLex.Core.HealthCheck\PharmaLex.Core.HealthCheck.csproj", "{DB6D4763-3880-46A3-B25D-B641B9554AA9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.Core.Configuration", "src\PharmaLex.Core.Configuration\PharmaLex.Core.Configuration.csproj", "{7BA78DF0-A5DF-41D5-B8DA-1456F6D461D3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tools", "tools", "{15CF7D6E-F48B-46D9-B6D4-BFE49DB25D1B}"
	ProjectSection(SolutionItems) = preProject
		tools\Add Journals - Portugal.sql = tools\Add Journals - Portugal.sql
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "sql", "sql", "{AC6A4F2E-2772-4A80-B2B8-27A94996C7F2}"
	ProjectSection(SolutionItems) = preProject
		tools\spDeleteImport.sql = tools\spDeleteImport.sql
		tools\spDeleteImportBatch.sql = tools\spDeleteImportBatch.sql
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Aggregators.PubMed", "src\PharmaLex.VigiLit.Aggregators.PubMed\PharmaLex.VigiLit.Aggregators.PubMed.csproj", "{6CA0A932-1828-4F73-AC16-DAF49CF9F309}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Core.Aggregator", "src\PharmaLex.VigiLit.Core.Aggregator\PharmaLex.VigiLit.Core.Aggregator.csproj", "{CC822AF0-E5E1-48E0-BBE0-FD5CC6C846BF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Aggregators.Crossref", "src\PharmaLex.VigiLit.Aggregators.Crossref\PharmaLex.VigiLit.Aggregators.Crossref.csproj", "{4EBCF3C6-27CC-4EB1-829A-53539EEFED2B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Reports.Accovion.Domain", "src\PharmaLex.VigiLit.Reports.Accovion.Domain\PharmaLex.VigiLit.Reports.Accovion.Domain.csproj", "{ED6DC5AA-09BA-441E-A137-1D6A10F1241F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.Dotnet.Adapters", "src\PharmaLex.Dotnet.Adapters\PharmaLex.Dotnet.Adapters.csproj", "{067E8C1D-3F2F-490E-B6E2-4A337A1734E1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.Core.Web", "src\PharmaLex.Core.Web\PharmaLex.Core.Web.csproj", "{1F0E7850-EAA1-40CB-8EF4-891AEA5B4C16}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Ui.ViewModels", "src\PharmaLex.VigiLit.Ui.ViewModels\PharmaLex.VigiLit.Ui.ViewModels.csproj", "{EA913EB8-CC72-4638-8667-5CFD36CE2182}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Reporting.Ui", "src\PharmaLex.VigiLit.Reporting.Ui\PharmaLex.VigiLit.Reporting.Ui.csproj", "{2C7F1063-2FA9-47D6-892D-08D29231C9BB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Reports.Apogepha.Domain", "src\PharmaLex.VigiLit.Reports.Apogepha.Domain\PharmaLex.VigiLit.Reports.Apogepha.Domain.csproj", "{CF664665-2010-4BA7-B3C5-8336C343E2EB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Reports.Merz.Domain", "src\PharmaLex.VigiLit.Reports.Merz.Domain\PharmaLex.VigiLit.Reports.Merz.Domain.csproj", "{A7AE0BB3-276A-4ABF-A618-1D277911B4CC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.Core.UserManagement", "src\PharmaLex.Core.UserManagement\PharmaLex.Core.UserManagement.csproj", "{CB376DC6-2C48-4FE1-835D-6EB36E681C66}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Reports.TrackingSheets.Domain", "src\PharmaLex.VigiLit.Reports.TrackingSheets.Domain\PharmaLex.VigiLit.Reports.TrackingSheets.Domain.csproj", "{03E30F71-DB06-4498-95BF-B40C64304950}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Company", "src\PharmaLex.VigiLit.Company\PharmaLex.VigiLit.Company.csproj", "{1E05186F-9D5A-409B-9FBB-39EFC942E13D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.Core.UserSessionManagement.Entities", "src\PharmaLex.Core.UserSessionManagement.Entities\PharmaLex.Core.UserSessionManagement.Entities.csproj", "{2BE69BCC-45D1-442E-BF35-A86CDD025A66}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.Core.UserSessionManagement", "src\PharmaLex.Core.UserSessionManagement\PharmaLex.Core.UserSessionManagement.csproj", "{71DA9245-846C-4030-BEFE-8D0A8B4349DD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.Core.UserSessionManagement.Integration.Tests", "test\PharmaLex.Core.UserSessionManagement.Integration.Tests\PharmaLex.Core.UserSessionManagement.Integration.Tests.csproj", "{50487E5B-8C0D-4594-808B-99A654DAADEE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ImportManagement", "src\PharmaLex.VigiLit.ImportManagement\PharmaLex.VigiLit.ImportManagement.csproj", "{F233E717-96FD-4261-A3FA-DAFBECB7DEE0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ReferenceManagement", "src\PharmaLex.VigiLit.ReferenceManagement\PharmaLex.VigiLit.ReferenceManagement.csproj", "{2287D585-7273-422C-BCBB-B4744A683F3D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.DataAccessLayer.Base", "src\PharmaLex.VigiLit.DataAccessLayer.Base\PharmaLex.VigiLit.DataAccessLayer.Base.csproj", "{F0E7F25B-8098-45ED-B6B6-105ED9DBFADF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ReferenceManagement.Contracts", "src\PharmaLex.VigiLit.ReferenceManagement.Contracts\PharmaLex.VigiLit.ReferenceManagement.Contracts.csproj", "{581DB454-229F-416B-9A25-438C9F4B7701}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ReferenceManagement.Service", "src\PharmaLex.VigiLit.ReferenceManagement.Service\PharmaLex.VigiLit.ReferenceManagement.Service.csproj", "{2B6F1BAC-579C-4DC0-9E06-D7BC2BF421AD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ImportManagement.Contracts", "src\PharmaLex.VigiLit.ImportManagement.Contracts\PharmaLex.VigiLit.ImportManagement.Contracts.csproj", "{DA7CC5B2-6981-439C-83EA-427F686C9E1E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ImportManagement.Service", "src\PharmaLex.VigiLit.ImportManagement.Service\PharmaLex.VigiLit.ImportManagement.Service.csproj", "{E698DF17-EDF9-417E-8483-F4524ED0D72E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ReferenceManagement.Service.Integration.Tests", "test\PharmaLex.VigiLit.ReferenceManagement.Service.Integration.Tests\PharmaLex.VigiLit.ReferenceManagement.Service.Integration.Tests.csproj", "{DC1546DB-D29E-48E5-9374-983E189FB136}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Test.DomainHelpers", "test\PharmaLex.VigiLit.Test.DomainHelpers\PharmaLex.VigiLit.Test.DomainHelpers.csproj", "{501C6CF0-3F5D-41BD-8298-F48E22AE9384}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ImportManagement.Service.Integration.Tests", "test\PharmaLex.VigiLit.ImportManagement.Service.Integration.Tests\PharmaLex.VigiLit.ImportManagement.Service.Integration.Tests.csproj", "{6562E288-A220-4960-B1D1-4F238781B99E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ContractManagement", "src\PharmaLex.VigiLit.ContractManagement\PharmaLex.VigiLit.ContractManagement.csproj", "{370B8CB5-FB6E-43C3-9D86-D9C01F23BD11}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.Core.DataAccessLayer", "src\PharmaLex.Core.DataAccessLayer\PharmaLex.Core.DataAccessLayer.csproj", "{335CC20E-8B9E-48DA-BADA-A902B756D9A1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ImportManagement.Documents", "src\PharmaLex.VigiLit.ImportManagement.Documents\PharmaLex.VigiLit.ImportManagement.Documents.csproj", "{3647084E-D2A0-4342-AD55-DE128642EEE8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ImportManagement.Ui", "src\PharmaLex.VigiLit.ImportManagement.Ui\PharmaLex.VigiLit.ImportManagement.Ui.csproj", "{C38D84EC-22A2-4DAC-ADA9-5D4010322288}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ImportManagement.Entities", "src\PharmaLex.VigiLit.ImportManagement.Entities\PharmaLex.VigiLit.ImportManagement.Entities.csproj", "{889AD190-0BE5-4885-8D79-F024BB1B765C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Core.Ui", "src\PharmaLex.VigiLit.Core.Ui\PharmaLex.VigiLit.Core.Ui.csproj", "{781CFD1D-EE28-4206-804B-C0F81D066103}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.Logging", "src\PharmaLex.VigiLit.Logging\PharmaLex.VigiLit.Logging.csproj", "{9D62FF83-3594-4DEB-8B20-3BF2F4CE3152}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.ContractManagement.Ui", "src\PharmaLex.VigiLit.ContractManagement.Ui\PharmaLex.VigiLit.ContractManagement.Ui.csproj", "{9ECC838A-B723-41BC-A468-BF1115627D1F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.DataExtraction.Entities", "src\PharmaLex.VigiLit.DataExtraction.Entities\PharmaLex.VigiLit.DataExtraction.Entities.csproj", "{0522822E-A619-4A53-9ABB-E05C85D24660}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PharmaLex.VigiLit.DataExtraction.Service", "src\PharmaLex.VigiLit.DataExtraction.Service\PharmaLex.VigiLit.DataExtraction.Service.csproj", "{CBA020C7-6722-408E-9EA4-C2B3131EDCAE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.ContractManagement.Service", "src\PharmaLex.VigiLit.ContractManagement.Service\PharmaLex.VigiLit.ContractManagement.Service.csproj", "{87AEE0DF-D84B-4881-8C60-0ABC48AC283E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "devops", "devops", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
	ProjectSection(SolutionItems) = preProject
		tools\devops\recreateMsSqlProcedure.sql = tools\devops\recreateMsSqlProcedure.sql
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{AB3AB2D0-C21F-4992-B737-CC26E2BF2FCA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PhlexVisionTestHarness", "tools\src\PhlexVisionTestHarness\PhlexVisionTestHarness.csproj", "{2D61882A-DD83-4584-9BCA-BC2D7CF93F76}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Application.Unit.Tests", "test\PharmaLex.VigiLit.Application.Unit.Tests\PharmaLex.VigiLit.Application.Unit.Tests.csproj", "{7E36250F-A1F3-6F38-D37C-6DE9DEC1B13F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.Core.HealthCheck.Unit.Tests", "test\PharmaLex.Core.HealthCheck.Unit.Tests\PharmaLex.Core.HealthCheck.Unit.Tests.csproj", "{185CA05F-BE88-5AC9-ACC4-CB728EA8761C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.Core.UserManagement.Unit.Tests", "test\PharmaLex.Core.UserManagement.Unit.Tests\PharmaLex.Core.UserManagement.Unit.Tests.csproj", "{F2B517E8-FE76-1B33-CD0D-134AF4CE669B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.Core.UserSessionManagement.Unit.Tests", "test\PharmaLex.Core.UserSessionManagement.Unit.Tests\PharmaLex.Core.UserSessionManagement.Unit.Tests.csproj", "{C862B2FF-7E8F-5D7A-42AA-6CF094F08599}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.AccessControl.Unit.Tests", "test\PharmaLex.VigiLit.AccessControl.Unit.Tests\PharmaLex.VigiLit.AccessControl.Unit.Tests.csproj", "{3DC2084B-D3A7-1FD5-8557-D8E489047887}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Aggregators.PubMed.Unit.Tests", "test\PharmaLex.VigiLit.Aggregators.PubMed.Unit.Tests\PharmaLex.VigiLit.Aggregators.PubMed.Unit.Tests.csproj", "{087BDB2E-DF07-5A79-3E44-646146DE79F8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.AiAnalysis.Unit.Tests", "test\PharmaLex.VigiLit.AiAnalysis.Unit.Tests\PharmaLex.VigiLit.AiAnalysis.Unit.Tests.csproj", "{1426964C-02D3-912A-DD7A-01A9EEB180D4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Application.Services.Unit.Tests", "test\PharmaLex.VigiLit.Application.Services.Unit.Tests\PharmaLex.VigiLit.Application.Services.Unit.Tests.csproj", "{980E7024-5815-1979-8493-4EE3F2AB470C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Case.Unit.Tests", "test\PharmaLex.VigiLit.Case.Unit.Tests\PharmaLex.VigiLit.Case.Unit.Tests.csproj", "{2FC35766-1C8F-FD4E-D9EB-3F5B76C72187}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.ContractManagement.Unit.Tests", "test\PharmaLex.VigiLit.ContractManagement.Unit.Tests\PharmaLex.VigiLit.ContractManagement.Unit.Tests.csproj", "{5A4E21DD-4E32-BE76-EF23-1D0C6789118B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.ContractManagement.Ui.Unit.Tests", "test\PharmaLex.VigiLit.ContractManagement.Ui.Unit.Tests\PharmaLex.VigiLit.ContractManagement.Ui.Unit.Tests.csproj", "{AB81954F-351E-D328-FA27-3295F1EB073A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Core.Aggregator.Unit.Tests", "test\PharmaLex.VigiLit.Core.Aggregator.Unit.Tests\PharmaLex.VigiLit.Core.Aggregator.Unit.Tests.csproj", "{ABE08825-8576-AD77-C189-F07F1AAD86E7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests", "test\PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests\PharmaLex.VigiLit.DataExtraction.Service.Unit.Tests.csproj", "{5B3D3A1E-19CF-FDD3-847A-2E882397DF65}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Domain.Unit.Tests", "test\PharmaLex.VigiLit.Domain.Unit.Tests\PharmaLex.VigiLit.Domain.Unit.Tests.csproj", "{C544815E-765A-9CAA-573D-79E9B5FC4388}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Ignore.Unit.Tests", "test\PharmaLex.VigiLit.Ignore.Unit.Tests\PharmaLex.VigiLit.Ignore.Unit.Tests.csproj", "{82BFD742-EF43-AFDB-2D99-A715CE78E064}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.ImportManagement.Document.Unit.Tests", "test\PharmaLex.VigiLit.ImportManagement.Document.Unit.Tests\PharmaLex.VigiLit.ImportManagement.Document.Unit.Tests.csproj", "{D6CD54D0-5318-64ED-AD2D-972ADFC3C97A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests", "test\PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests\PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests.csproj", "{A8EEDEB2-87DE-DD94-328E-C385863E82FC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.ImportManagement.Ui.Unit.Tests", "test\PharmaLex.VigiLit.ImportManagement.Ui.Unit.Tests\PharmaLex.VigiLit.ImportManagement.Ui.Unit.Tests.csproj", "{F6E08321-46A9-55C2-F618-06E3DCAD5D25}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Infrastructure.Unit.Tests", "test\PharmaLex.VigiLit.Infrastructure.Unit.Tests\PharmaLex.VigiLit.Infrastructure.Unit.Tests.csproj", "{1206F7DE-0DE8-13C3-E1F2-6258CE24FD6B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Logging.Unit.Tests", "test\PharmaLex.VigiLit.Logging.Unit.Tests\PharmaLex.VigiLit.Logging.Unit.Tests.csproj", "{D1629971-E3F2-16B0-5CCC-8B8318970024}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.ReferenceManagement.Service.Unit.Tests", "test\PharmaLex.VigiLit.ReferenceManagement.Service.Unit.Tests\PharmaLex.VigiLit.ReferenceManagement.Service.Unit.Tests.csproj", "{234012AB-42A0-6F16-E637-3B1332B0B969}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Reporting.Unit.Tests", "test\PharmaLex.VigiLit.Reporting.Unit.Tests\PharmaLex.VigiLit.Reporting.Unit.Tests.csproj", "{F72179F2-3113-94BB-F83F-83DBC7D1C1A5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Reports.Accovion.Domain.Unit.Tests", "test\PharmaLex.VigiLit.Reports.Accovion.Domain.Unit.Tests\PharmaLex.VigiLit.Reports.Accovion.Domain.Unit.Tests.csproj", "{335615D9-96F5-72EE-EF80-FC5BBC53E5C5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Reports.Apogepha.Domain.Unit.Tests", "test\PharmaLex.VigiLit.Reports.Apogepha.Domain.Unit.Tests\PharmaLex.VigiLit.Reports.Apogepha.Domain.Unit.Tests.csproj", "{9B20D7D1-7EF0-1B16-CB6C-D33C933E38F1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Reports.Merz.Domain.Unit.Tests", "test\PharmaLex.VigiLit.Reports.Merz.Domain.Unit.Tests\PharmaLex.VigiLit.Reports.Merz.Domain.Unit.Tests.csproj", "{B191FB73-B0C3-51B5-635D-4722B4E64278}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Unit.Tests", "test\PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Unit.Tests\PharmaLex.VigiLit.Reports.TrackingSheets.Domain.Unit.Tests.csproj", "{986F2771-C5AD-E72D-E295-62B12E650B2E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Ui.ViewModels.Unit.Tests", "test\PharmaLex.VigiLit.Ui.ViewModels.Unit.Tests\PharmaLex.VigiLit.Ui.ViewModels.Unit.Tests.csproj", "{7E45A107-21E7-9231-A8A0-E19E658512A3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Web.Unit.Tests", "test\PharmaLex.VigiLit.Web.Unit.Tests\PharmaLex.VigiLit.Web.Unit.Tests.csproj", "{CC736E88-FCA2-2FAC-0338-D4835B23A9A9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Infrastructure.Import.PubMed.Unit.Tests", "test\PharmaLex.VigiLit.Infrastructure.Import.PubMed.Unit.Tests\PharmaLex.VigiLit.Infrastructure.Import.PubMed.Unit.Tests.csproj", "{4723F986-91C0-5F11-E1E0-855DDDB643EC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.ImportManagement.Client", "src\PharmaLex.VigiLit.ImportManagement.Client\PharmaLex.VigiLit.ImportManagement.Client.csproj", "{E9D2E4C6-66BF-C99A-57CC-EE8A1FDB0F4F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Scraping.Client", "src\PharmaLex.VigiLit.Scraping.Client\PharmaLex.VigiLit.Scraping.Client.csproj", "{9377FD4F-DEFA-4AE0-802F-183BE9636861}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Scraping.Contracts", "src\PharmaLex.VigiLit.Scraping.Contracts\PharmaLex.VigiLit.Scraping.Contracts.csproj", "{793E2F29-2203-4389-9D26-289CBBE80670}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Scraping.Entities", "src\PharmaLex.VigiLit.Scraping.Entities\PharmaLex.VigiLit.Scraping.Entities.csproj", "{05EEE701-1DCE-4566-8D2C-E9DBB3770F36}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Scraping.Service", "src\PharmaLex.VigiLit.Scraping.Service\PharmaLex.VigiLit.Scraping.Service.csproj", "{EF22BB14-6497-41EA-BA69-761AD4AAF669}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Scraping.Service.Integration.Tests", "test\PharmaLex.VigiLit.Scraping.Service.Integration.Tests\PharmaLex.VigiLit.Scraping.Service.Integration.Tests.csproj", "{D70E8845-6A5B-4670-91A7-D9FEA85CAA06}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.Scraping.Service.Unit.Tests", "test\PharmaLex.VigiLit.Scraping.Service.Unit.Tests\PharmaLex.VigiLit.Scraping.Service.Unit.Tests.csproj", "{AB8A79D2-14E7-4314-8A47-80E017FAA3DE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PharmaLex.VigiLit.DataExtraction.Client", "src\PharmaLex.VigiLit.DataExtraction.Client\PharmaLex.VigiLit.DataExtraction.Client.csproj", "{2E7D9697-0CE3-FD47-0C30-37B7D43554E6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Phlex.Core.Apify", "..\Phlex.Core.Apify\src\Phlex.Core.Apify\Phlex.Core.Apify.csproj", "{0092EB2B-B9B5-ECAB-8B10-914B603E8C2A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Phlex.Core.Apify.Webhook", "..\Phlex.Core.Apify\src\Phlex.Core.Apify.Webhook\Phlex.Core.Apify.Webhook.csproj", "{4F52CD3B-6550-B55C-5297-8B053CBE5D7D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F48742C9-5762-4F8C-A6FE-DBB786D20BC1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F48742C9-5762-4F8C-A6FE-DBB786D20BC1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F48742C9-5762-4F8C-A6FE-DBB786D20BC1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F48742C9-5762-4F8C-A6FE-DBB786D20BC1}.Release|Any CPU.Build.0 = Release|Any CPU
		{13162086-EAA0-4F04-9362-0CD840991171}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{13162086-EAA0-4F04-9362-0CD840991171}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{13162086-EAA0-4F04-9362-0CD840991171}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{13162086-EAA0-4F04-9362-0CD840991171}.Release|Any CPU.Build.0 = Release|Any CPU
		{DD7EFF21-975F-45BD-86E5-4E8BEA64C8A7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD7EFF21-975F-45BD-86E5-4E8BEA64C8A7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD7EFF21-975F-45BD-86E5-4E8BEA64C8A7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD7EFF21-975F-45BD-86E5-4E8BEA64C8A7}.Release|Any CPU.Build.0 = Release|Any CPU
		{D6B94273-1C9D-4339-A4C7-7C3E5ECCBEC9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D6B94273-1C9D-4339-A4C7-7C3E5ECCBEC9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D6B94273-1C9D-4339-A4C7-7C3E5ECCBEC9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D6B94273-1C9D-4339-A4C7-7C3E5ECCBEC9}.Release|Any CPU.Build.0 = Release|Any CPU
		{898D4FA7-7FEB-4DCC-A832-8B6EB71A59CB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{898D4FA7-7FEB-4DCC-A832-8B6EB71A59CB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{898D4FA7-7FEB-4DCC-A832-8B6EB71A59CB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{898D4FA7-7FEB-4DCC-A832-8B6EB71A59CB}.Release|Any CPU.Build.0 = Release|Any CPU
		{37662230-5F49-417D-8DDA-16201E9782DE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{37662230-5F49-417D-8DDA-16201E9782DE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{37662230-5F49-417D-8DDA-16201E9782DE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{37662230-5F49-417D-8DDA-16201E9782DE}.Release|Any CPU.Build.0 = Release|Any CPU
		{EB982B83-C16C-4598-B3F1-77D0D406CF00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EB982B83-C16C-4598-B3F1-77D0D406CF00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EB982B83-C16C-4598-B3F1-77D0D406CF00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EB982B83-C16C-4598-B3F1-77D0D406CF00}.Release|Any CPU.Build.0 = Release|Any CPU
		{198627AE-7997-44C9-9F1B-2F1E5DC344EC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{198627AE-7997-44C9-9F1B-2F1E5DC344EC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{198627AE-7997-44C9-9F1B-2F1E5DC344EC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{198627AE-7997-44C9-9F1B-2F1E5DC344EC}.Release|Any CPU.Build.0 = Release|Any CPU
		{006D24E9-8BEA-4D62-9657-0EAFA878E800}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{006D24E9-8BEA-4D62-9657-0EAFA878E800}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{006D24E9-8BEA-4D62-9657-0EAFA878E800}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{006D24E9-8BEA-4D62-9657-0EAFA878E800}.Release|Any CPU.Build.0 = Release|Any CPU
		{8EB85008-C351-45FD-B3F7-2A1A0178ECE7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8EB85008-C351-45FD-B3F7-2A1A0178ECE7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8EB85008-C351-45FD-B3F7-2A1A0178ECE7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8EB85008-C351-45FD-B3F7-2A1A0178ECE7}.Release|Any CPU.Build.0 = Release|Any CPU
		{44D805D2-CF1A-4A54-A098-8D5D81DF6B21}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44D805D2-CF1A-4A54-A098-8D5D81DF6B21}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44D805D2-CF1A-4A54-A098-8D5D81DF6B21}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44D805D2-CF1A-4A54-A098-8D5D81DF6B21}.Release|Any CPU.Build.0 = Release|Any CPU
		{79A3F0F3-DE7E-4C80-BB3E-7700DD17E898}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{79A3F0F3-DE7E-4C80-BB3E-7700DD17E898}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{79A3F0F3-DE7E-4C80-BB3E-7700DD17E898}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{79A3F0F3-DE7E-4C80-BB3E-7700DD17E898}.Release|Any CPU.Build.0 = Release|Any CPU
		{0DF41E4F-7719-486D-8DB6-3347BC274C44}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0DF41E4F-7719-486D-8DB6-3347BC274C44}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0DF41E4F-7719-486D-8DB6-3347BC274C44}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0DF41E4F-7719-486D-8DB6-3347BC274C44}.Release|Any CPU.Build.0 = Release|Any CPU
		{4AC7E4EB-859C-4BFA-B7E1-FF5E67B83F49}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4AC7E4EB-859C-4BFA-B7E1-FF5E67B83F49}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4AC7E4EB-859C-4BFA-B7E1-FF5E67B83F49}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4AC7E4EB-859C-4BFA-B7E1-FF5E67B83F49}.Release|Any CPU.Build.0 = Release|Any CPU
		{6BA48300-BF93-4C7F-9B79-C839BAB2BF59}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6BA48300-BF93-4C7F-9B79-C839BAB2BF59}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6BA48300-BF93-4C7F-9B79-C839BAB2BF59}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6BA48300-BF93-4C7F-9B79-C839BAB2BF59}.Release|Any CPU.Build.0 = Release|Any CPU
		{316F2B4F-B6C6-4947-9859-A4746CC4A647}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{316F2B4F-B6C6-4947-9859-A4746CC4A647}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{316F2B4F-B6C6-4947-9859-A4746CC4A647}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{316F2B4F-B6C6-4947-9859-A4746CC4A647}.Release|Any CPU.Build.0 = Release|Any CPU
		{86BDC0E6-0F86-4867-8E7C-37AA09C6FB02}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{86BDC0E6-0F86-4867-8E7C-37AA09C6FB02}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{86BDC0E6-0F86-4867-8E7C-37AA09C6FB02}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{86BDC0E6-0F86-4867-8E7C-37AA09C6FB02}.Release|Any CPU.Build.0 = Release|Any CPU
		{7D6B217D-34E3-40F6-A9E6-E9270143789D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7D6B217D-34E3-40F6-A9E6-E9270143789D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7D6B217D-34E3-40F6-A9E6-E9270143789D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7D6B217D-34E3-40F6-A9E6-E9270143789D}.Release|Any CPU.Build.0 = Release|Any CPU
		{FB69913C-960D-40AF-84E9-AC90C6529033}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FB69913C-960D-40AF-84E9-AC90C6529033}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FB69913C-960D-40AF-84E9-AC90C6529033}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FB69913C-960D-40AF-84E9-AC90C6529033}.Release|Any CPU.Build.0 = Release|Any CPU
		{9CBC2B88-2AEE-433B-A1A5-D30B7EC05442}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9CBC2B88-2AEE-433B-A1A5-D30B7EC05442}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9CBC2B88-2AEE-433B-A1A5-D30B7EC05442}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9CBC2B88-2AEE-433B-A1A5-D30B7EC05442}.Release|Any CPU.Build.0 = Release|Any CPU
		{FFB530E3-C877-4D8C-9D78-CB51FB585D91}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FFB530E3-C877-4D8C-9D78-CB51FB585D91}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FFB530E3-C877-4D8C-9D78-CB51FB585D91}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FFB530E3-C877-4D8C-9D78-CB51FB585D91}.Release|Any CPU.Build.0 = Release|Any CPU
		{5253F2F4-71D4-47F0-AB9D-1C4AE8740EA1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5253F2F4-71D4-47F0-AB9D-1C4AE8740EA1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5253F2F4-71D4-47F0-AB9D-1C4AE8740EA1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5253F2F4-71D4-47F0-AB9D-1C4AE8740EA1}.Release|Any CPU.Build.0 = Release|Any CPU
		{BEAA2497-372B-4DC7-8AAC-75936FB1CB01}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BEAA2497-372B-4DC7-8AAC-75936FB1CB01}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BEAA2497-372B-4DC7-8AAC-75936FB1CB01}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BEAA2497-372B-4DC7-8AAC-75936FB1CB01}.Release|Any CPU.Build.0 = Release|Any CPU
		{22ACC837-6B65-4E53-8CD7-3C346E22BC1A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{22ACC837-6B65-4E53-8CD7-3C346E22BC1A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{22ACC837-6B65-4E53-8CD7-3C346E22BC1A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{22ACC837-6B65-4E53-8CD7-3C346E22BC1A}.Release|Any CPU.Build.0 = Release|Any CPU
		{F9117B6C-1E26-4637-963E-0DCDB39FB544}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F9117B6C-1E26-4637-963E-0DCDB39FB544}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F9117B6C-1E26-4637-963E-0DCDB39FB544}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F9117B6C-1E26-4637-963E-0DCDB39FB544}.Release|Any CPU.Build.0 = Release|Any CPU
		{DB6D4763-3880-46A3-B25D-B641B9554AA9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DB6D4763-3880-46A3-B25D-B641B9554AA9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DB6D4763-3880-46A3-B25D-B641B9554AA9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DB6D4763-3880-46A3-B25D-B641B9554AA9}.Release|Any CPU.Build.0 = Release|Any CPU
		{7BA78DF0-A5DF-41D5-B8DA-1456F6D461D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7BA78DF0-A5DF-41D5-B8DA-1456F6D461D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7BA78DF0-A5DF-41D5-B8DA-1456F6D461D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7BA78DF0-A5DF-41D5-B8DA-1456F6D461D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{6CA0A932-1828-4F73-AC16-DAF49CF9F309}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6CA0A932-1828-4F73-AC16-DAF49CF9F309}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6CA0A932-1828-4F73-AC16-DAF49CF9F309}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6CA0A932-1828-4F73-AC16-DAF49CF9F309}.Release|Any CPU.Build.0 = Release|Any CPU
		{CC822AF0-E5E1-48E0-BBE0-FD5CC6C846BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CC822AF0-E5E1-48E0-BBE0-FD5CC6C846BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CC822AF0-E5E1-48E0-BBE0-FD5CC6C846BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CC822AF0-E5E1-48E0-BBE0-FD5CC6C846BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{4EBCF3C6-27CC-4EB1-829A-53539EEFED2B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4EBCF3C6-27CC-4EB1-829A-53539EEFED2B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4EBCF3C6-27CC-4EB1-829A-53539EEFED2B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4EBCF3C6-27CC-4EB1-829A-53539EEFED2B}.Release|Any CPU.Build.0 = Release|Any CPU
		{ED6DC5AA-09BA-441E-A137-1D6A10F1241F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ED6DC5AA-09BA-441E-A137-1D6A10F1241F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ED6DC5AA-09BA-441E-A137-1D6A10F1241F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ED6DC5AA-09BA-441E-A137-1D6A10F1241F}.Release|Any CPU.Build.0 = Release|Any CPU
		{067E8C1D-3F2F-490E-B6E2-4A337A1734E1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{067E8C1D-3F2F-490E-B6E2-4A337A1734E1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{067E8C1D-3F2F-490E-B6E2-4A337A1734E1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{067E8C1D-3F2F-490E-B6E2-4A337A1734E1}.Release|Any CPU.Build.0 = Release|Any CPU
		{1F0E7850-EAA1-40CB-8EF4-891AEA5B4C16}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1F0E7850-EAA1-40CB-8EF4-891AEA5B4C16}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1F0E7850-EAA1-40CB-8EF4-891AEA5B4C16}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1F0E7850-EAA1-40CB-8EF4-891AEA5B4C16}.Release|Any CPU.Build.0 = Release|Any CPU
		{EA913EB8-CC72-4638-8667-5CFD36CE2182}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EA913EB8-CC72-4638-8667-5CFD36CE2182}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EA913EB8-CC72-4638-8667-5CFD36CE2182}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EA913EB8-CC72-4638-8667-5CFD36CE2182}.Release|Any CPU.Build.0 = Release|Any CPU
		{2C7F1063-2FA9-47D6-892D-08D29231C9BB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2C7F1063-2FA9-47D6-892D-08D29231C9BB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2C7F1063-2FA9-47D6-892D-08D29231C9BB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2C7F1063-2FA9-47D6-892D-08D29231C9BB}.Release|Any CPU.Build.0 = Release|Any CPU
		{CF664665-2010-4BA7-B3C5-8336C343E2EB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CF664665-2010-4BA7-B3C5-8336C343E2EB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CF664665-2010-4BA7-B3C5-8336C343E2EB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CF664665-2010-4BA7-B3C5-8336C343E2EB}.Release|Any CPU.Build.0 = Release|Any CPU
		{A7AE0BB3-276A-4ABF-A618-1D277911B4CC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A7AE0BB3-276A-4ABF-A618-1D277911B4CC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A7AE0BB3-276A-4ABF-A618-1D277911B4CC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A7AE0BB3-276A-4ABF-A618-1D277911B4CC}.Release|Any CPU.Build.0 = Release|Any CPU
		{CB376DC6-2C48-4FE1-835D-6EB36E681C66}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CB376DC6-2C48-4FE1-835D-6EB36E681C66}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CB376DC6-2C48-4FE1-835D-6EB36E681C66}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CB376DC6-2C48-4FE1-835D-6EB36E681C66}.Release|Any CPU.Build.0 = Release|Any CPU
		{03E30F71-DB06-4498-95BF-B40C64304950}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{03E30F71-DB06-4498-95BF-B40C64304950}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{03E30F71-DB06-4498-95BF-B40C64304950}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{03E30F71-DB06-4498-95BF-B40C64304950}.Release|Any CPU.Build.0 = Release|Any CPU
		{1E05186F-9D5A-409B-9FBB-39EFC942E13D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E05186F-9D5A-409B-9FBB-39EFC942E13D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E05186F-9D5A-409B-9FBB-39EFC942E13D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E05186F-9D5A-409B-9FBB-39EFC942E13D}.Release|Any CPU.Build.0 = Release|Any CPU
		{2BE69BCC-45D1-442E-BF35-A86CDD025A66}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2BE69BCC-45D1-442E-BF35-A86CDD025A66}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2BE69BCC-45D1-442E-BF35-A86CDD025A66}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2BE69BCC-45D1-442E-BF35-A86CDD025A66}.Release|Any CPU.Build.0 = Release|Any CPU
		{71DA9245-846C-4030-BEFE-8D0A8B4349DD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{71DA9245-846C-4030-BEFE-8D0A8B4349DD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{71DA9245-846C-4030-BEFE-8D0A8B4349DD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{71DA9245-846C-4030-BEFE-8D0A8B4349DD}.Release|Any CPU.Build.0 = Release|Any CPU
		{50487E5B-8C0D-4594-808B-99A654DAADEE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50487E5B-8C0D-4594-808B-99A654DAADEE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{50487E5B-8C0D-4594-808B-99A654DAADEE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{50487E5B-8C0D-4594-808B-99A654DAADEE}.Release|Any CPU.Build.0 = Release|Any CPU
		{F233E717-96FD-4261-A3FA-DAFBECB7DEE0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F233E717-96FD-4261-A3FA-DAFBECB7DEE0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F233E717-96FD-4261-A3FA-DAFBECB7DEE0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F233E717-96FD-4261-A3FA-DAFBECB7DEE0}.Release|Any CPU.Build.0 = Release|Any CPU
		{2287D585-7273-422C-BCBB-B4744A683F3D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2287D585-7273-422C-BCBB-B4744A683F3D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2287D585-7273-422C-BCBB-B4744A683F3D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2287D585-7273-422C-BCBB-B4744A683F3D}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0E7F25B-8098-45ED-B6B6-105ED9DBFADF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0E7F25B-8098-45ED-B6B6-105ED9DBFADF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0E7F25B-8098-45ED-B6B6-105ED9DBFADF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0E7F25B-8098-45ED-B6B6-105ED9DBFADF}.Release|Any CPU.Build.0 = Release|Any CPU
		{581DB454-229F-416B-9A25-438C9F4B7701}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{581DB454-229F-416B-9A25-438C9F4B7701}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{581DB454-229F-416B-9A25-438C9F4B7701}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{581DB454-229F-416B-9A25-438C9F4B7701}.Release|Any CPU.Build.0 = Release|Any CPU
		{2B6F1BAC-579C-4DC0-9E06-D7BC2BF421AD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2B6F1BAC-579C-4DC0-9E06-D7BC2BF421AD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2B6F1BAC-579C-4DC0-9E06-D7BC2BF421AD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2B6F1BAC-579C-4DC0-9E06-D7BC2BF421AD}.Release|Any CPU.Build.0 = Release|Any CPU
		{DA7CC5B2-6981-439C-83EA-427F686C9E1E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DA7CC5B2-6981-439C-83EA-427F686C9E1E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DA7CC5B2-6981-439C-83EA-427F686C9E1E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DA7CC5B2-6981-439C-83EA-427F686C9E1E}.Release|Any CPU.Build.0 = Release|Any CPU
		{E698DF17-EDF9-417E-8483-F4524ED0D72E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E698DF17-EDF9-417E-8483-F4524ED0D72E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E698DF17-EDF9-417E-8483-F4524ED0D72E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E698DF17-EDF9-417E-8483-F4524ED0D72E}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC1546DB-D29E-48E5-9374-983E189FB136}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC1546DB-D29E-48E5-9374-983E189FB136}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC1546DB-D29E-48E5-9374-983E189FB136}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC1546DB-D29E-48E5-9374-983E189FB136}.Release|Any CPU.Build.0 = Release|Any CPU
		{501C6CF0-3F5D-41BD-8298-F48E22AE9384}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{501C6CF0-3F5D-41BD-8298-F48E22AE9384}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{501C6CF0-3F5D-41BD-8298-F48E22AE9384}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{501C6CF0-3F5D-41BD-8298-F48E22AE9384}.Release|Any CPU.Build.0 = Release|Any CPU
		{6562E288-A220-4960-B1D1-4F238781B99E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6562E288-A220-4960-B1D1-4F238781B99E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6562E288-A220-4960-B1D1-4F238781B99E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6562E288-A220-4960-B1D1-4F238781B99E}.Release|Any CPU.Build.0 = Release|Any CPU
		{370B8CB5-FB6E-43C3-9D86-D9C01F23BD11}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{370B8CB5-FB6E-43C3-9D86-D9C01F23BD11}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{370B8CB5-FB6E-43C3-9D86-D9C01F23BD11}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{370B8CB5-FB6E-43C3-9D86-D9C01F23BD11}.Release|Any CPU.Build.0 = Release|Any CPU
		{335CC20E-8B9E-48DA-BADA-A902B756D9A1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{335CC20E-8B9E-48DA-BADA-A902B756D9A1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{335CC20E-8B9E-48DA-BADA-A902B756D9A1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{335CC20E-8B9E-48DA-BADA-A902B756D9A1}.Release|Any CPU.Build.0 = Release|Any CPU
		{3647084E-D2A0-4342-AD55-DE128642EEE8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3647084E-D2A0-4342-AD55-DE128642EEE8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3647084E-D2A0-4342-AD55-DE128642EEE8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3647084E-D2A0-4342-AD55-DE128642EEE8}.Release|Any CPU.Build.0 = Release|Any CPU
		{C38D84EC-22A2-4DAC-ADA9-5D4010322288}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C38D84EC-22A2-4DAC-ADA9-5D4010322288}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C38D84EC-22A2-4DAC-ADA9-5D4010322288}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C38D84EC-22A2-4DAC-ADA9-5D4010322288}.Release|Any CPU.Build.0 = Release|Any CPU
		{889AD190-0BE5-4885-8D79-F024BB1B765C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{889AD190-0BE5-4885-8D79-F024BB1B765C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{889AD190-0BE5-4885-8D79-F024BB1B765C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{889AD190-0BE5-4885-8D79-F024BB1B765C}.Release|Any CPU.Build.0 = Release|Any CPU
		{781CFD1D-EE28-4206-804B-C0F81D066103}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{781CFD1D-EE28-4206-804B-C0F81D066103}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{781CFD1D-EE28-4206-804B-C0F81D066103}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{781CFD1D-EE28-4206-804B-C0F81D066103}.Release|Any CPU.Build.0 = Release|Any CPU
		{9D62FF83-3594-4DEB-8B20-3BF2F4CE3152}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9D62FF83-3594-4DEB-8B20-3BF2F4CE3152}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9D62FF83-3594-4DEB-8B20-3BF2F4CE3152}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9D62FF83-3594-4DEB-8B20-3BF2F4CE3152}.Release|Any CPU.Build.0 = Release|Any CPU
		{9ECC838A-B723-41BC-A468-BF1115627D1F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9ECC838A-B723-41BC-A468-BF1115627D1F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9ECC838A-B723-41BC-A468-BF1115627D1F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9ECC838A-B723-41BC-A468-BF1115627D1F}.Release|Any CPU.Build.0 = Release|Any CPU
		{0522822E-A619-4A53-9ABB-E05C85D24660}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0522822E-A619-4A53-9ABB-E05C85D24660}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0522822E-A619-4A53-9ABB-E05C85D24660}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0522822E-A619-4A53-9ABB-E05C85D24660}.Release|Any CPU.Build.0 = Release|Any CPU
		{CBA020C7-6722-408E-9EA4-C2B3131EDCAE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CBA020C7-6722-408E-9EA4-C2B3131EDCAE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CBA020C7-6722-408E-9EA4-C2B3131EDCAE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CBA020C7-6722-408E-9EA4-C2B3131EDCAE}.Release|Any CPU.Build.0 = Release|Any CPU
		{87AEE0DF-D84B-4881-8C60-0ABC48AC283E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{87AEE0DF-D84B-4881-8C60-0ABC48AC283E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{87AEE0DF-D84B-4881-8C60-0ABC48AC283E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{87AEE0DF-D84B-4881-8C60-0ABC48AC283E}.Release|Any CPU.Build.0 = Release|Any CPU
		{2D61882A-DD83-4584-9BCA-BC2D7CF93F76}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2D61882A-DD83-4584-9BCA-BC2D7CF93F76}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2D61882A-DD83-4584-9BCA-BC2D7CF93F76}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2D61882A-DD83-4584-9BCA-BC2D7CF93F76}.Release|Any CPU.Build.0 = Release|Any CPU
		{7E36250F-A1F3-6F38-D37C-6DE9DEC1B13F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7E36250F-A1F3-6F38-D37C-6DE9DEC1B13F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7E36250F-A1F3-6F38-D37C-6DE9DEC1B13F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7E36250F-A1F3-6F38-D37C-6DE9DEC1B13F}.Release|Any CPU.Build.0 = Release|Any CPU
		{185CA05F-BE88-5AC9-ACC4-CB728EA8761C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{185CA05F-BE88-5AC9-ACC4-CB728EA8761C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{185CA05F-BE88-5AC9-ACC4-CB728EA8761C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{185CA05F-BE88-5AC9-ACC4-CB728EA8761C}.Release|Any CPU.Build.0 = Release|Any CPU
		{F2B517E8-FE76-1B33-CD0D-134AF4CE669B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F2B517E8-FE76-1B33-CD0D-134AF4CE669B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F2B517E8-FE76-1B33-CD0D-134AF4CE669B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F2B517E8-FE76-1B33-CD0D-134AF4CE669B}.Release|Any CPU.Build.0 = Release|Any CPU
		{C862B2FF-7E8F-5D7A-42AA-6CF094F08599}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C862B2FF-7E8F-5D7A-42AA-6CF094F08599}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C862B2FF-7E8F-5D7A-42AA-6CF094F08599}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C862B2FF-7E8F-5D7A-42AA-6CF094F08599}.Release|Any CPU.Build.0 = Release|Any CPU
		{3DC2084B-D3A7-1FD5-8557-D8E489047887}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3DC2084B-D3A7-1FD5-8557-D8E489047887}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3DC2084B-D3A7-1FD5-8557-D8E489047887}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3DC2084B-D3A7-1FD5-8557-D8E489047887}.Release|Any CPU.Build.0 = Release|Any CPU
		{087BDB2E-DF07-5A79-3E44-646146DE79F8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{087BDB2E-DF07-5A79-3E44-646146DE79F8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{087BDB2E-DF07-5A79-3E44-646146DE79F8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{087BDB2E-DF07-5A79-3E44-646146DE79F8}.Release|Any CPU.Build.0 = Release|Any CPU
		{1426964C-02D3-912A-DD7A-01A9EEB180D4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1426964C-02D3-912A-DD7A-01A9EEB180D4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1426964C-02D3-912A-DD7A-01A9EEB180D4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1426964C-02D3-912A-DD7A-01A9EEB180D4}.Release|Any CPU.Build.0 = Release|Any CPU
		{980E7024-5815-1979-8493-4EE3F2AB470C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{980E7024-5815-1979-8493-4EE3F2AB470C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{980E7024-5815-1979-8493-4EE3F2AB470C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{980E7024-5815-1979-8493-4EE3F2AB470C}.Release|Any CPU.Build.0 = Release|Any CPU
		{2FC35766-1C8F-FD4E-D9EB-3F5B76C72187}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2FC35766-1C8F-FD4E-D9EB-3F5B76C72187}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2FC35766-1C8F-FD4E-D9EB-3F5B76C72187}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2FC35766-1C8F-FD4E-D9EB-3F5B76C72187}.Release|Any CPU.Build.0 = Release|Any CPU
		{5A4E21DD-4E32-BE76-EF23-1D0C6789118B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A4E21DD-4E32-BE76-EF23-1D0C6789118B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A4E21DD-4E32-BE76-EF23-1D0C6789118B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A4E21DD-4E32-BE76-EF23-1D0C6789118B}.Release|Any CPU.Build.0 = Release|Any CPU
		{AB81954F-351E-D328-FA27-3295F1EB073A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB81954F-351E-D328-FA27-3295F1EB073A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB81954F-351E-D328-FA27-3295F1EB073A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB81954F-351E-D328-FA27-3295F1EB073A}.Release|Any CPU.Build.0 = Release|Any CPU
		{ABE08825-8576-AD77-C189-F07F1AAD86E7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ABE08825-8576-AD77-C189-F07F1AAD86E7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ABE08825-8576-AD77-C189-F07F1AAD86E7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ABE08825-8576-AD77-C189-F07F1AAD86E7}.Release|Any CPU.Build.0 = Release|Any CPU
		{5B3D3A1E-19CF-FDD3-847A-2E882397DF65}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5B3D3A1E-19CF-FDD3-847A-2E882397DF65}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5B3D3A1E-19CF-FDD3-847A-2E882397DF65}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5B3D3A1E-19CF-FDD3-847A-2E882397DF65}.Release|Any CPU.Build.0 = Release|Any CPU
		{C544815E-765A-9CAA-573D-79E9B5FC4388}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C544815E-765A-9CAA-573D-79E9B5FC4388}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C544815E-765A-9CAA-573D-79E9B5FC4388}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C544815E-765A-9CAA-573D-79E9B5FC4388}.Release|Any CPU.Build.0 = Release|Any CPU
		{82BFD742-EF43-AFDB-2D99-A715CE78E064}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{82BFD742-EF43-AFDB-2D99-A715CE78E064}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{82BFD742-EF43-AFDB-2D99-A715CE78E064}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{82BFD742-EF43-AFDB-2D99-A715CE78E064}.Release|Any CPU.Build.0 = Release|Any CPU
		{D6CD54D0-5318-64ED-AD2D-972ADFC3C97A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D6CD54D0-5318-64ED-AD2D-972ADFC3C97A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D6CD54D0-5318-64ED-AD2D-972ADFC3C97A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D6CD54D0-5318-64ED-AD2D-972ADFC3C97A}.Release|Any CPU.Build.0 = Release|Any CPU
		{A8EEDEB2-87DE-DD94-328E-C385863E82FC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A8EEDEB2-87DE-DD94-328E-C385863E82FC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A8EEDEB2-87DE-DD94-328E-C385863E82FC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A8EEDEB2-87DE-DD94-328E-C385863E82FC}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6E08321-46A9-55C2-F618-06E3DCAD5D25}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6E08321-46A9-55C2-F618-06E3DCAD5D25}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6E08321-46A9-55C2-F618-06E3DCAD5D25}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6E08321-46A9-55C2-F618-06E3DCAD5D25}.Release|Any CPU.Build.0 = Release|Any CPU
		{1206F7DE-0DE8-13C3-E1F2-6258CE24FD6B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1206F7DE-0DE8-13C3-E1F2-6258CE24FD6B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1206F7DE-0DE8-13C3-E1F2-6258CE24FD6B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1206F7DE-0DE8-13C3-E1F2-6258CE24FD6B}.Release|Any CPU.Build.0 = Release|Any CPU
		{D1629971-E3F2-16B0-5CCC-8B8318970024}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D1629971-E3F2-16B0-5CCC-8B8318970024}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D1629971-E3F2-16B0-5CCC-8B8318970024}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D1629971-E3F2-16B0-5CCC-8B8318970024}.Release|Any CPU.Build.0 = Release|Any CPU
		{234012AB-42A0-6F16-E637-3B1332B0B969}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{234012AB-42A0-6F16-E637-3B1332B0B969}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{234012AB-42A0-6F16-E637-3B1332B0B969}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{234012AB-42A0-6F16-E637-3B1332B0B969}.Release|Any CPU.Build.0 = Release|Any CPU
		{F72179F2-3113-94BB-F83F-83DBC7D1C1A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F72179F2-3113-94BB-F83F-83DBC7D1C1A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F72179F2-3113-94BB-F83F-83DBC7D1C1A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F72179F2-3113-94BB-F83F-83DBC7D1C1A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{335615D9-96F5-72EE-EF80-FC5BBC53E5C5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{335615D9-96F5-72EE-EF80-FC5BBC53E5C5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{335615D9-96F5-72EE-EF80-FC5BBC53E5C5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{335615D9-96F5-72EE-EF80-FC5BBC53E5C5}.Release|Any CPU.Build.0 = Release|Any CPU
		{9B20D7D1-7EF0-1B16-CB6C-D33C933E38F1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9B20D7D1-7EF0-1B16-CB6C-D33C933E38F1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9B20D7D1-7EF0-1B16-CB6C-D33C933E38F1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9B20D7D1-7EF0-1B16-CB6C-D33C933E38F1}.Release|Any CPU.Build.0 = Release|Any CPU
		{B191FB73-B0C3-51B5-635D-4722B4E64278}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B191FB73-B0C3-51B5-635D-4722B4E64278}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B191FB73-B0C3-51B5-635D-4722B4E64278}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B191FB73-B0C3-51B5-635D-4722B4E64278}.Release|Any CPU.Build.0 = Release|Any CPU
		{986F2771-C5AD-E72D-E295-62B12E650B2E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{986F2771-C5AD-E72D-E295-62B12E650B2E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{986F2771-C5AD-E72D-E295-62B12E650B2E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{986F2771-C5AD-E72D-E295-62B12E650B2E}.Release|Any CPU.Build.0 = Release|Any CPU
		{7E45A107-21E7-9231-A8A0-E19E658512A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7E45A107-21E7-9231-A8A0-E19E658512A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7E45A107-21E7-9231-A8A0-E19E658512A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7E45A107-21E7-9231-A8A0-E19E658512A3}.Release|Any CPU.Build.0 = Release|Any CPU
		{CC736E88-FCA2-2FAC-0338-D4835B23A9A9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CC736E88-FCA2-2FAC-0338-D4835B23A9A9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CC736E88-FCA2-2FAC-0338-D4835B23A9A9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CC736E88-FCA2-2FAC-0338-D4835B23A9A9}.Release|Any CPU.Build.0 = Release|Any CPU
		{4723F986-91C0-5F11-E1E0-855DDDB643EC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4723F986-91C0-5F11-E1E0-855DDDB643EC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4723F986-91C0-5F11-E1E0-855DDDB643EC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4723F986-91C0-5F11-E1E0-855DDDB643EC}.Release|Any CPU.Build.0 = Release|Any CPU
		{E9D2E4C6-66BF-C99A-57CC-EE8A1FDB0F4F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9D2E4C6-66BF-C99A-57CC-EE8A1FDB0F4F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E9D2E4C6-66BF-C99A-57CC-EE8A1FDB0F4F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E9D2E4C6-66BF-C99A-57CC-EE8A1FDB0F4F}.Release|Any CPU.Build.0 = Release|Any CPU
		{9377FD4F-DEFA-4AE0-802F-183BE9636861}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9377FD4F-DEFA-4AE0-802F-183BE9636861}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9377FD4F-DEFA-4AE0-802F-183BE9636861}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9377FD4F-DEFA-4AE0-802F-183BE9636861}.Release|Any CPU.Build.0 = Release|Any CPU
		{793E2F29-2203-4389-9D26-289CBBE80670}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{793E2F29-2203-4389-9D26-289CBBE80670}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{793E2F29-2203-4389-9D26-289CBBE80670}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{793E2F29-2203-4389-9D26-289CBBE80670}.Release|Any CPU.Build.0 = Release|Any CPU
		{05EEE701-1DCE-4566-8D2C-E9DBB3770F36}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{05EEE701-1DCE-4566-8D2C-E9DBB3770F36}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{05EEE701-1DCE-4566-8D2C-E9DBB3770F36}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{05EEE701-1DCE-4566-8D2C-E9DBB3770F36}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF22BB14-6497-41EA-BA69-761AD4AAF669}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF22BB14-6497-41EA-BA69-761AD4AAF669}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF22BB14-6497-41EA-BA69-761AD4AAF669}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF22BB14-6497-41EA-BA69-761AD4AAF669}.Release|Any CPU.Build.0 = Release|Any CPU
		{D70E8845-6A5B-4670-91A7-D9FEA85CAA06}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D70E8845-6A5B-4670-91A7-D9FEA85CAA06}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D70E8845-6A5B-4670-91A7-D9FEA85CAA06}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D70E8845-6A5B-4670-91A7-D9FEA85CAA06}.Release|Any CPU.Build.0 = Release|Any CPU
		{AB8A79D2-14E7-4314-8A47-80E017FAA3DE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB8A79D2-14E7-4314-8A47-80E017FAA3DE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB8A79D2-14E7-4314-8A47-80E017FAA3DE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB8A79D2-14E7-4314-8A47-80E017FAA3DE}.Release|Any CPU.Build.0 = Release|Any CPU
		{2E7D9697-0CE3-FD47-0C30-37B7D43554E6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2E7D9697-0CE3-FD47-0C30-37B7D43554E6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2E7D9697-0CE3-FD47-0C30-37B7D43554E6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2E7D9697-0CE3-FD47-0C30-37B7D43554E6}.Release|Any CPU.Build.0 = Release|Any CPU
		{0092EB2B-B9B5-ECAB-8B10-914B603E8C2A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0092EB2B-B9B5-ECAB-8B10-914B603E8C2A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0092EB2B-B9B5-ECAB-8B10-914B603E8C2A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0092EB2B-B9B5-ECAB-8B10-914B603E8C2A}.Release|Any CPU.Build.0 = Release|Any CPU
		{4F52CD3B-6550-B55C-5297-8B053CBE5D7D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4F52CD3B-6550-B55C-5297-8B053CBE5D7D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4F52CD3B-6550-B55C-5297-8B053CBE5D7D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4F52CD3B-6550-B55C-5297-8B053CBE5D7D}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F48742C9-5762-4F8C-A6FE-DBB786D20BC1} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{13162086-EAA0-4F04-9362-0CD840991171} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{DD7EFF21-975F-45BD-86E5-4E8BEA64C8A7} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{D6B94273-1C9D-4339-A4C7-7C3E5ECCBEC9} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{C0463953-9BFB-4410-9DDD-EBB7F0EBA8E1} = {E72FF5D7-F294-493D-85FA-88AD5BEB120E}
		{898D4FA7-7FEB-4DCC-A832-8B6EB71A59CB} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{37662230-5F49-417D-8DDA-16201E9782DE} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{EB982B83-C16C-4598-B3F1-77D0D406CF00} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{198627AE-7997-44C9-9F1B-2F1E5DC344EC} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{006D24E9-8BEA-4D62-9657-0EAFA878E800} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{8EB85008-C351-45FD-B3F7-2A1A0178ECE7} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{44D805D2-CF1A-4A54-A098-8D5D81DF6B21} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{79A3F0F3-DE7E-4C80-BB3E-7700DD17E898} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{0DF41E4F-7719-486D-8DB6-3347BC274C44} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{4AC7E4EB-859C-4BFA-B7E1-FF5E67B83F49} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{6BA48300-BF93-4C7F-9B79-C839BAB2BF59} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{316F2B4F-B6C6-4947-9859-A4746CC4A647} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{86BDC0E6-0F86-4867-8E7C-37AA09C6FB02} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{7D6B217D-34E3-40F6-A9E6-E9270143789D} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{FB69913C-960D-40AF-84E9-AC90C6529033} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{9CBC2B88-2AEE-433B-A1A5-D30B7EC05442} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{FFB530E3-C877-4D8C-9D78-CB51FB585D91} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{5253F2F4-71D4-47F0-AB9D-1C4AE8740EA1} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{BEAA2497-372B-4DC7-8AAC-75936FB1CB01} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{22ACC837-6B65-4E53-8CD7-3C346E22BC1A} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{F9117B6C-1E26-4637-963E-0DCDB39FB544} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{DB6D4763-3880-46A3-B25D-B641B9554AA9} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{7BA78DF0-A5DF-41D5-B8DA-1456F6D461D3} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{AC6A4F2E-2772-4A80-B2B8-27A94996C7F2} = {15CF7D6E-F48B-46D9-B6D4-BFE49DB25D1B}
		{6CA0A932-1828-4F73-AC16-DAF49CF9F309} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{CC822AF0-E5E1-48E0-BBE0-FD5CC6C846BF} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{4EBCF3C6-27CC-4EB1-829A-53539EEFED2B} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{ED6DC5AA-09BA-441E-A137-1D6A10F1241F} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{067E8C1D-3F2F-490E-B6E2-4A337A1734E1} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{1F0E7850-EAA1-40CB-8EF4-891AEA5B4C16} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{EA913EB8-CC72-4638-8667-5CFD36CE2182} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{2C7F1063-2FA9-47D6-892D-08D29231C9BB} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{CF664665-2010-4BA7-B3C5-8336C343E2EB} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{A7AE0BB3-276A-4ABF-A618-1D277911B4CC} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{CB376DC6-2C48-4FE1-835D-6EB36E681C66} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{03E30F71-DB06-4498-95BF-B40C64304950} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{1E05186F-9D5A-409B-9FBB-39EFC942E13D} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{2BE69BCC-45D1-442E-BF35-A86CDD025A66} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{71DA9245-846C-4030-BEFE-8D0A8B4349DD} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{50487E5B-8C0D-4594-808B-99A654DAADEE} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{F233E717-96FD-4261-A3FA-DAFBECB7DEE0} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{2287D585-7273-422C-BCBB-B4744A683F3D} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{F0E7F25B-8098-45ED-B6B6-105ED9DBFADF} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{581DB454-229F-416B-9A25-438C9F4B7701} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{2B6F1BAC-579C-4DC0-9E06-D7BC2BF421AD} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{DA7CC5B2-6981-439C-83EA-427F686C9E1E} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{E698DF17-EDF9-417E-8483-F4524ED0D72E} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{DC1546DB-D29E-48E5-9374-983E189FB136} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{501C6CF0-3F5D-41BD-8298-F48E22AE9384} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{6562E288-A220-4960-B1D1-4F238781B99E} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{370B8CB5-FB6E-43C3-9D86-D9C01F23BD11} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{335CC20E-8B9E-48DA-BADA-A902B756D9A1} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{3647084E-D2A0-4342-AD55-DE128642EEE8} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{C38D84EC-22A2-4DAC-ADA9-5D4010322288} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{889AD190-0BE5-4885-8D79-F024BB1B765C} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{781CFD1D-EE28-4206-804B-C0F81D066103} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{9D62FF83-3594-4DEB-8B20-3BF2F4CE3152} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{9ECC838A-B723-41BC-A468-BF1115627D1F} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{0522822E-A619-4A53-9ABB-E05C85D24660} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{CBA020C7-6722-408E-9EA4-C2B3131EDCAE} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{87AEE0DF-D84B-4881-8C60-0ABC48AC283E} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{02EA681E-C7D8-13C7-8484-4AC65E1B71E8} = {15CF7D6E-F48B-46D9-B6D4-BFE49DB25D1B}
		{AB3AB2D0-C21F-4992-B737-CC26E2BF2FCA} = {15CF7D6E-F48B-46D9-B6D4-BFE49DB25D1B}
		{2D61882A-DD83-4584-9BCA-BC2D7CF93F76} = {AB3AB2D0-C21F-4992-B737-CC26E2BF2FCA}
		{7E36250F-A1F3-6F38-D37C-6DE9DEC1B13F} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{185CA05F-BE88-5AC9-ACC4-CB728EA8761C} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{F2B517E8-FE76-1B33-CD0D-134AF4CE669B} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{C862B2FF-7E8F-5D7A-42AA-6CF094F08599} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{3DC2084B-D3A7-1FD5-8557-D8E489047887} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{087BDB2E-DF07-5A79-3E44-646146DE79F8} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{1426964C-02D3-912A-DD7A-01A9EEB180D4} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{980E7024-5815-1979-8493-4EE3F2AB470C} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{2FC35766-1C8F-FD4E-D9EB-3F5B76C72187} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{5A4E21DD-4E32-BE76-EF23-1D0C6789118B} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{AB81954F-351E-D328-FA27-3295F1EB073A} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{ABE08825-8576-AD77-C189-F07F1AAD86E7} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{5B3D3A1E-19CF-FDD3-847A-2E882397DF65} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{C544815E-765A-9CAA-573D-79E9B5FC4388} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{82BFD742-EF43-AFDB-2D99-A715CE78E064} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{D6CD54D0-5318-64ED-AD2D-972ADFC3C97A} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{A8EEDEB2-87DE-DD94-328E-C385863E82FC} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{F6E08321-46A9-55C2-F618-06E3DCAD5D25} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{1206F7DE-0DE8-13C3-E1F2-6258CE24FD6B} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{D1629971-E3F2-16B0-5CCC-8B8318970024} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{234012AB-42A0-6F16-E637-3B1332B0B969} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{F72179F2-3113-94BB-F83F-83DBC7D1C1A5} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{335615D9-96F5-72EE-EF80-FC5BBC53E5C5} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{9B20D7D1-7EF0-1B16-CB6C-D33C933E38F1} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{B191FB73-B0C3-51B5-635D-4722B4E64278} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{986F2771-C5AD-E72D-E295-62B12E650B2E} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{7E45A107-21E7-9231-A8A0-E19E658512A3} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{CC736E88-FCA2-2FAC-0338-D4835B23A9A9} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{4723F986-91C0-5F11-E1E0-855DDDB643EC} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{E9D2E4C6-66BF-C99A-57CC-EE8A1FDB0F4F} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{9377FD4F-DEFA-4AE0-802F-183BE9636861} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{793E2F29-2203-4389-9D26-289CBBE80670} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{05EEE701-1DCE-4566-8D2C-E9DBB3770F36} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{EF22BB14-6497-41EA-BA69-761AD4AAF669} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
		{D70E8845-6A5B-4670-91A7-D9FEA85CAA06} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{AB8A79D2-14E7-4314-8A47-80E017FAA3DE} = {F256B9A9-A9D9-4816-8BC1-56DE888AC25A}
		{2E7D9697-0CE3-FD47-0C30-37B7D43554E6} = {81A75B69-6E43-43F6-93D6-2BB2D25F7659}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {69747315-3ACC-4AD0-9BD5-40781B60028B}
	EndGlobalSection
EndGlobal
