@using Microsoft.AspNetCore.Authorization;
@using Microsoft.AspNetCore.Mvc.TagHelpers
@using Microsoft.Extensions.Configuration
@using PharmaLex.Core.Web.Extensions
@using PharmaLex.Core.Web.Models.UserNotification
@using PharmaLex.VigiLit.Domain.UserManagement
@using AppSettingsHelper = PharmaLex.Core.Configuration.AppSettingsHelper
@using Microsoft.FeatureManagement

@inject IFeatureManager FeatureManager

@inject IConfiguration Configuration
@inject IAuthorizationService AuthorizationService
@inject AppSettingsHelper AppSettings
@inject CdnHelper Cdn

@{
    var backgroundClass = string.Empty;
    var isLoginView = ViewBag.ShowBackground ?? false;

    if (isLoginView)
    {
        backgroundClass = "has-background";
    }
    const string enableFileImport = "EnableFileImportManualEntry";
    var isFileImportFlagEnabled = await FeatureManager.IsEnabledAsync(enableFileImport);


    var pollingTime = Configuration.GetValue<string>("UserSession:SessionPollingTimeMilliseconds");
}

<!DOCTYPE html>
<html lang="en">
<head>
    <title>VigiLit :: @ViewData["Title"]</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="VigiLit is a Cencora PharmaLex internal system for literature screening">
    <link rel="shortcut icon" type="image/png" href="@Cdn.GetUrl("images/favicon/cencora-favicon.png")">

    <meta name="apple-mobile-web-app-title" content="PharmaLex">
    <meta name="application-name" content="PharmaLex">
    <meta name="theme-color" content="#233c4c">

    <link rel="stylesheet" href="@Cdn.Host/css/fonts.css" />
    <link rel="stylesheet" href="@Cdn.Host/css/shared.css" />
    <link rel="stylesheet" href="~/dist/css/vigiLitCss.css" asp-append-version="true" />

    <script src="@Cdn.Host/js/plx.js"></script>
    <script src="@Cdn.Host/js/toast.js"></script>
    <script src="@Cdn.Host/lib/tippy/popper/1.15/popper.min.js"></script>
    <script src="@Cdn.Host/lib/tippy/4.3.5/tippy.min.js"></script>
    <script src="@Cdn.Host/lib/vue/3.2.6/vue.prod.js"></script>
    <script src="@Cdn.Host/lib/moment/moment.min.js"></script>

    <script src="@Cdn.Host/lib/jquery/dist/jquery.min.js"></script>
    <script src="@Cdn.Host/lib/jquery-validation/dist/jquery.validate.js"></script>
    <script src="@Cdn.Host/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"></script>

    <script src="~/dist/js/modal.js" asp-append-version="true"></script>
    <script src="~/dist/js/downloadFile.js" asp-append-version="true"></script>
    <script src="~/dist/js/localStorageValidator.js" asp-append-version="true"></script>

    <script src="~/lib/jsdiff/diff.min.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Styles", required: false)
</head>
<body>

    <div class="core-container">
        <header id="page-header" class="page-header">
            <a class="app-title" href="/">
                VigiLit
            </a>
            @if (User.Identity.IsAuthenticated)
            {
                string controller = this.ViewContext.RouteData.Values["controller"]?.ToString();
                <div class="main-navigation-wrapper">
                    <nav aria-label="main-navigation" class="main-navigation">
                        <ul class="nav-menu">
                            <li class="@("Home" == controller ? "selected" : "")"><a href="/">Home</a></li>

                            @if ((await AuthorizationService.AuthorizeAsync(User, Policies.Assessor)).Succeeded)
                            {
                                <li class="@("PreClassification" == controller ? "selected" : "")"><a href="/PreClassification">Pre-Classification</a></li>
                            }

                            @if ((await AuthorizationService.AuthorizeAsync(User, Policies.MasterAssessor)).Succeeded)
                            {
                                <li class="@("Classification" == controller ? "selected" : "")"><a href="/Classification">Classification</a></li>
                            }

                            @if ((await AuthorizationService.AuthorizeAsync(User, Policies.CaseExternal)).Succeeded)
                            {
                                <li class="@("Cases" == controller ? "selected" : "")"><a href="/Cases">Cases</a></li>
                            }

                            @if ((await AuthorizationService.AuthorizeAsync(User, Policies.CaseManagement)).Succeeded)
                            {
                                <li class="@("Cases" == controller ? "selected" : "")">
                                    <a class="flyout-header" href="">Cases</a> <i class="m-icon">expand_more</i>
                                    <div class="flyout">
                                        <ul>
                                            <li><a href="/Cases/Pending">Pending</a></li>
                                            <li><a href="/Cases/Published">Published</a></li>
                                        </ul>
                                    </div>
                                </li>
                            }
                            else if ((await AuthorizationService.AuthorizeAsync(User, Policies.InternalUser)).Succeeded)
                            {
                                <li class="@("Cases" == controller ? "selected" : "")"><a href="/Cases/Published">Cases</a></li>
                            }

                            @if ((await AuthorizationService.AuthorizeAsync(User, Policies.ExternalUser)).Succeeded)
                            {
                                <li class="@("ContractHistory" == controller ? "selected" : "")"><a href="/ContractHistory">Contracts</a></li>
                            }

                            <li class="@("Search" == controller ? "selected" : "")"><a href="/Search">Search</a></li>

                            @if ((await AuthorizationService.AuthorizeAsync(User, Policies.Admin)).Succeeded)
                            {
                                <li class="@(new List<string>{"Users", "Assessors", "Substances", "Contracts", "Emails", "Companies", "Locks"}.Contains(controller) ? "selected" : "")">
                                    <a class="flyout-header" href="">Manage</a> <i class="m-icon">expand_more</i>
                                    <div class="flyout">
                                        <ul>
                                            <li><a href="/Users/<USER>">Users</a></li>
                                            <li><a href="/Assessors">Assessors</a></li>
                                            <li><a href="/Substances">Substances</a></li>
                                            <li><a href="/Contracts">Contracts</a></li>
                                            <li><a href="/Emails">Email Log</a></li>
                                            <li><a href="/Companies">Companies</a></li>
                                            <li><a href="/Locks">Locks</a></li>
                                        </ul>
                                    </div>
                                </li>
                                <li class="@(new List<string>{"ImportLog",}.Contains(controller) ? "selected" : "")">
                                    <a class="flyout-header" href="">Import</a> <i class="m-icon">expand_more</i>
                                    <div class="flyout">
                                        <ul>
                                            <li><a href="/Import/ImportLog">Import Log</a></li>
                                            <li><a href="/Import/AdHocList">Ad-Hoc Import</a></li>
                                            @if (isFileImportFlagEnabled)
                                            {
                                                <li><a href="/Import/FileImport">File Import</a></li>
                                            }
                                        </ul>
                                    </div>
                                </li>
                            }
                            <li class="@(new List<string>{"Reports", "TrackingSheets"}.Contains(controller) ? "selected" : "")"><a href="/Reports">Reports</a></li>
                            <li class="@("Help" == controller ? "selected" : "")">
                                <a class="flyout-header" href="">Help</a> <i class="m-icon">expand_more</i>
                                <div class="flyout">
                                    <ul>
                                        <li><a href="https://customerservice.phlexglobal.com/hc/en-us/requests/new" target="_blank" rel="noopener noreferrer">Contact</a></li>
                                        <li><a href="/Help">About</a></li>
                                    </ul>
                                </div>
                            </li>
                        </ul>
                    </nav>
                </div>
                <nav aria-label="account-navigation" class="account-navigation">
                    <ul class="nav-menu">
                        @if ((await AuthorizationService.AuthorizeAsync(User, Policies.SuperAdmin)).Succeeded)
                        {

                            <li>
                                <a class="flyout-header account" href=""><span class="nav-menu-user-icon">settings</span></a>
                                <div class="flyout">
                                    <ul>
                                        <li><a href="/settings">Settings</a></li>
                                    </ul>
                                </div>
                            </li>
                        }
                        <li>
                            <a class="flyout-header account" href=""><span class="nav-menu-user-icon">account_circle</span>@User.Claims.First(x => x.Type == "name").Value</a>
                            <div class="flyout">
                                <ul>
                                    <li><a href="/logoutex">Sign out</a></li>
                                </ul>
                            </div>
                        </li>
                    </ul>
                </nav>
            }
        </header>

        <div class="core-content @backgroundClass" id="vue-app">
            @RenderBody()
        </div>
    </div>

    @await RenderSectionAsync("Scripts", required: false)

    <script>
        if (typeof pageConfig !== 'undefined' && pageConfig.appElement) {
            var vueApp = Vue.createApp({ mixins: [pageConfig] });
        }
    </script>

    @await RenderSectionAsync("VueComponentScripts", required: false)

    <script>
        //mount vue instance here with components now attached(if they exist)
        if (typeof pageConfig !== 'undefined' && pageConfig.appElement) {
            vueApp.mount(pageConfig.appElement);
        }

        // Accessibility: set sub menus to expand when Return key is hit when they have focus (via keyboard)
        Array.from(document.getElementsByClassName('flyout-header')).forEach(x => x.onclick = function () {
            let element = this.parentElement.getElementsByClassName('flyout')[0];
            element.style.display = element.style.display == "flex" ? "" : "flex";
            return false;
        });
    </script>

    <script>
        var timerInterval = setInterval(
            function getServerSession() {
                fetch("/SessionIsValid", {
                    method: "GET",
                    credentials: 'same-origin',
                }).then(response => {
                    if (!response.ok) {
                        throw response;
                    }
                    return response.json();
                }).then(data => {
                    if (!data) {
                        clearInterval(timerInterval);
                        alert("You have been signed out");
                        window.location.href = "/logoutex";
                    }
                }).catch(error => {
                    return false;
                });
            }, @pollingTime);

    </script>

    @if (this.TempData.TryGet<UserNotificationModel>("UserNotification", out UserNotificationModel un))
    {
        <script type="text/javascript">
            plx.toast.show('@un.Html', @un.Position, '@un.Type', @(un.Number.HasValue ? un.Number.ToString() : "null"), @un.Duration);
        </script>
    }

</body>
</html>
